import 'package:flutter/material.dart';

/// A modern card widget with consistent styling and shadow effects
class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double borderRadius;
  final List<BoxShadow>? boxShadow;
  final Border? border;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius = 16,
    this.boxShadow,
    this.border,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBoxShadow = boxShadow ?? [
      BoxShadow(
        color: Colors.grey.withValues(alpha: 0.1),
        blurRadius: 10,
        offset: const Offset(0, 4),
      ),
    ];

    Widget cardContent = Container(
      width: width,
      height: height,
      padding: padding ?? const EdgeInsets.all(16),
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: effectiveBoxShadow,
        border: border,
      ),
      child: child,
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: cardContent,
        ),
      );
    }

    return cardContent;
  }
}

/// A modern error card for displaying error messages
class ModernErrorCard extends StatelessWidget {
  final String message;
  final IconData icon;
  final VoidCallback? onDismiss;
  final EdgeInsetsGeometry? margin;

  const ModernErrorCard({
    super.key,
    required this.message,
    this.icon = Icons.error_outline,
    this.onDismiss,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      backgroundColor: Colors.red.shade50,
      border: Border.all(color: Colors.red.shade200),
      boxShadow: [
        BoxShadow(
          color: Colors.red.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.red.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: Colors.red.shade600,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ],
      ),
    );
  }
}

/// A modern success card for displaying success messages
class ModernSuccessCard extends StatelessWidget {
  final String message;
  final IconData icon;
  final VoidCallback? onDismiss;
  final EdgeInsetsGeometry? margin;

  const ModernSuccessCard({
    super.key,
    required this.message,
    this.icon = Icons.check_circle_outline,
    this.onDismiss,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      backgroundColor: Colors.green.shade50,
      border: Border.all(color: Colors.green.shade200),
      boxShadow: [
        BoxShadow(
          color: Colors.green.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.green.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: Colors.green.shade600,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ],
      ),
    );
  }
}

/// A modern info card for displaying informational messages
class ModernInfoCard extends StatelessWidget {
  final String message;
  final IconData icon;
  final VoidCallback? onDismiss;
  final EdgeInsetsGeometry? margin;
  final Color? color;

  const ModernInfoCard({
    super.key,
    required this.message,
    this.icon = Icons.info_outline,
    this.onDismiss,
    this.margin,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? Colors.blue;
    
    return ModernCard(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      backgroundColor: effectiveColor.withValues(alpha: 0.1),
      border: Border.all(color: effectiveColor.withValues(alpha: 0.3)),
      boxShadow: [
        BoxShadow(
          color: effectiveColor.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      child: Row(
        children: [
          Icon(
            icon,
            color: effectiveColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: effectiveColor.withValues(alpha: 0.8),
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: effectiveColor,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ],
      ),
    );
  }
}
