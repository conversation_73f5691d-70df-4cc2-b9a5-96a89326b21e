# Category Image Fallback System

## Overview
The ChickenFresh user app now includes a smart fallback system for product images. When a product doesn't have an image uploaded by the admin, the app will automatically use the appropriate category image instead.

## How It Works

### 1. Image Priority Order
1. **Product Image** - If the product has an uploaded image, it will be displayed
2. **Category Image** - If no product image exists, the app uses the category's image from `assets/images/categories/`
3. **Category Icon** - If the category image is also missing, a category-specific icon is shown
4. **Generic Icon** - As a final fallback, a generic restaurant icon is displayed

### 2. Category Mapping
The system maps product categories to their corresponding images:

```dart
'chicken' → 'assets/images/categories/chicken.png'
'boneless chicken' → 'assets/images/categories/boneless_chicken.png'
'chicken legs' → 'assets/images/categories/chicken_legs.png'
'chicken breast' → 'assets/images/categories/chicken_breast.png'
'chicken wings' → 'assets/images/categories/chicken_wings.png'
'whole chicken' → 'assets/images/categories/whole_chicken.png'
'fish' → 'assets/images/categories/fish.png'
'mutton' → 'assets/images/categories/mutton.png'
'ready to cook' → 'assets/images/categories/ready_to_cook.png'
'cold cuts' → 'assets/images/categories/cold_cuts.png'
'plant based' → 'assets/images/categories/plant_based.png'
'spreads' → 'assets/images/categories/spreads.png'
'other' → 'assets/images/categories/other.png'
'general' → 'assets/images/categories/other.png'
```

### 3. Implementation Details

#### CategoryImageService
- **Location**: `lib/services/category_image_service.dart`
- **Main Method**: `buildProductImage()` - Creates a widget that handles the fallback logic
- **Helper Methods**: 
  - `getCategoryImagePath()` - Returns the asset path for a category
  - `getCategoryFallbackIcon()` - Returns the appropriate icon for a category
  - `getCategoryColor()` - Returns the theme color for a category

#### Updated Screens
The following screens now use the category fallback system:
- **Products List Screen** (`products_list_screen.dart`)
- **Product Detail Screen** (`product_detail_screen.dart`)
- **Cart Screen** (`cart_screen.dart`)
- **Order Summary Screen** (`order_summary_screen.dart`)

### 4. Usage Example

```dart
// Old way (only product image or generic icon)
child: product.imageUrl.isNotEmpty
    ? Image.network(product.imageUrl, fit: BoxFit.cover)
    : Icon(Icons.restaurant),

// New way (with category fallback)
child: CategoryImageService.buildProductImage(
  productImageUrl: product.imageUrl,
  category: product.category,
  width: 80,
  height: 80,
  fit: BoxFit.cover,
  borderRadius: BorderRadius.circular(8),
),
```

### 5. Benefits

1. **Better User Experience** - Products without images now show relevant category images instead of generic icons
2. **Visual Consistency** - All products in the same category share a consistent visual theme
3. **Professional Appearance** - The app looks more polished and complete
4. **Reduced Admin Burden** - Admins don't need to upload images for every single product
5. **Graceful Degradation** - Multiple fallback levels ensure something meaningful is always displayed

### 6. Adding New Categories

To add a new category:

1. Add the category image to `assets/images/categories/`
2. Update the `_categoryData` map in `CategoryImageService`
3. Include the new asset in `pubspec.yaml` if needed

### 7. Asset Requirements

Category images should be:
- **Format**: PNG with transparent background
- **Size**: 64x64 pixels minimum (128x128 recommended)
- **Style**: Simple, clear, and recognizable
- **Colors**: Should work well with both light and colored backgrounds

## Technical Implementation

The system uses a centralized service (`CategoryImageService`) that:
- Maps category names to asset paths and fallback icons
- Provides a unified widget builder method
- Handles all error cases gracefully
- Maintains consistent styling across the app

This ensures that every product image display in the app automatically benefits from the category fallback system without requiring individual screen modifications.
