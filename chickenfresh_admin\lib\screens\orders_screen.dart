
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/order.dart' as order_model;
import '../services/order_service.dart';

class OrdersScreen extends StatefulWidget {
  final order_model.OrderStatus? initialStatus;

  const OrdersScreen({super.key, this.initialStatus});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  final OrderService _orderService = OrderService();
  order_model.OrderStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.initialStatus;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_selectedStatus != null
            ? '${_selectedStatus!.name.toUpperCase()} Orders'
            : 'Orders Management'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: _selectedStatus != null ? [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _selectedStatus = null;
              });
            },
            tooltip: 'Clear Filter',
          ),
        ] : null,
      ),
      body: Column(
        children: [
        // Status filter
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text('Filter by status: '),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButton<order_model.OrderStatus?>(
                  value: _selectedStatus,
                  isExpanded: true,
                  hint: const Text('All Orders'),
                  onChanged: (order_model.OrderStatus? newValue) {
                    setState(() {
                      _selectedStatus = newValue;
                    });
                  },
                  items: [
                    const DropdownMenuItem<order_model.OrderStatus?>(
                      value: null,
                      child: Text('All Orders'),
                    ),
                    ...order_model.OrderStatus.values.map((order_model.OrderStatus status) {
                      return DropdownMenuItem<order_model.OrderStatus?>(
                        value: status,
                        child: Text(status.name.toUpperCase()),
                      );
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Orders list
        Expanded(
          child: StreamBuilder<List<order_model.Order>>(
            stream: _selectedStatus != null
                ? _orderService.getOrdersByStatus(_selectedStatus!)
                : _orderService.getOrders(),
            builder: (context, snapshot) {
              if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              }

              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(child: Text('No orders yet.'));
              }

              final orders = snapshot.data!;

              return ListView.builder(
                itemCount: orders.length,
                itemBuilder: (context, index) {
                  final order = orders[index];

                  return Card(
                    margin: const EdgeInsets.all(8.0),
                    child: ExpansionTile(
                      title: Text('Order #${order.orderNumber}'),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Customer: ${order.customerInfo.name}'),
                          Text('Status: ${order.statusDisplayName}'),
                          Text('Total: ${order.formattedTotalAmount}'),
                        ],
                      ),
                      trailing: _buildStatusChip(order.status),
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Customer Info
                              const Text('Customer Information:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                              Text('Name: ${order.customerInfo.name}'),
                              Text('Phone: ${order.customerInfo.phoneNumber}'),
                              if (order.customerInfo.email != null)
                                Text('Email: ${order.customerInfo.email}'),

                              const SizedBox(height: 16),

                              // Delivery Address
                              const Text('Delivery Address:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                              Text(order.deliveryAddress.fullAddress),

                              const SizedBox(height: 16),

                              // Order Items
                              const Text('Order Items:',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                              ...order.items.map((item) => Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                child: Text(
                                  '• ${item.productName} x ${item.quantity} = ₹${item.totalPrice.toStringAsFixed(2)}'
                                ),
                              )),

                              const SizedBox(height: 16),

                              // Order Summary
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text('Subtotal:', style: TextStyle(fontWeight: FontWeight.bold)),
                                  Text(order.formattedSubtotal),
                                ],
                              ),
                              if (order.deliveryFee > 0)
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Delivery Fee:'),
                                    Text(order.formattedDeliveryFee),
                                  ],
                                ),
                              const Divider(),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text('Total:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                  Text(order.formattedTotalAmount, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                ],
                              ),

                              const SizedBox(height: 16),



                              // Order Actions
                              Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      if (order.status == order_model.OrderStatus.pending)
                                        ElevatedButton(
                                          onPressed: () => _updateOrderStatus(order.id, order_model.OrderStatus.confirmed),
                                          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                                          child: const Text('Confirm'),
                                        ),
                                      if (order.status == order_model.OrderStatus.confirmed)
                                        ElevatedButton(
                                          onPressed: () => _updateOrderStatus(order.id, order_model.OrderStatus.processing),
                                          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                                          child: const Text('Process'),
                                        ),

                                      if (order.status == order_model.OrderStatus.outForDelivery)
                                        ElevatedButton(
                                          onPressed: () => _updateOrderStatus(order.id, order_model.OrderStatus.delivered),
                                          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                                          child: const Text('Mark Delivered'),
                                        ),
                                    ],
                                  ),
                                  if (order.canBeCancelled) ...[
                                    const SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: [
                                        ElevatedButton(
                                          onPressed: () => _cancelOrder(order.id),
                                          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                                          child: const Text('Cancel Order'),
                                        ),
                                        if (order.canBePartiallyCancelled)
                                          ElevatedButton(
                                            onPressed: () => _partialCancelOrder(order),
                                            style: ElevatedButton.styleFrom(backgroundColor: Colors.deepOrange),
                                            child: const Text('Partial Cancel'),
                                          ),
                                      ],
                                    ),
                                  ],
                                ],
                              ),

                              const SizedBox(height: 8),
                              Text('Order Date: ${order.createdAt.toLocal().toString().split('.')[0]}'),
                              if (order.notes != null)
                                Text('Notes: ${order.notes}'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
      ),
    );
  }

  Widget _buildStatusChip(order_model.OrderStatus status) {
    Color color;
    switch (status) {
      case order_model.OrderStatus.pending:
        color = Colors.orange;
        break;
      case order_model.OrderStatus.confirmed:
        color = Colors.blue;
        break;
      case order_model.OrderStatus.processing:
        color = Colors.purple;
        break;
      case order_model.OrderStatus.outForDelivery:
        color = Colors.amber;
        break;
      case order_model.OrderStatus.delivered:
        color = Colors.green;
        break;
      case order_model.OrderStatus.cancelled:
        color = Colors.red;
        break;
      case order_model.OrderStatus.partialCancelled:
        color = Colors.deepOrange;
        break;
      case order_model.OrderStatus.refunded:
        color = Colors.grey;
        break;
    }

    return Chip(
      label: Text(
        status.name.toUpperCase(),
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  Future<void> _updateOrderStatus(String orderId, order_model.OrderStatus newStatus) async {
    try {
      if (kDebugMode) {
        print('Attempting to update order $orderId to status ${newStatus.name}');
      }

      await _orderService.updateOrderStatus(orderId, newStatus);

      if (kDebugMode) {
        print('Order status update completed successfully');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.name}'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Force a rebuild to show updated status
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating order status: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to update order status: $e')),
        );
      }
    }
  }

  Future<void> _cancelOrder(String orderId) async {
    final reason = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Cancel Order'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Please provide a reason for cancellation:'),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  hintText: 'Cancellation reason',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(controller.text),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Confirm Cancellation'),
            ),
          ],
        );
      },
    );

    if (reason != null && reason.isNotEmpty) {
      try {
        await _orderService.cancelOrder(orderId, reason);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Order cancelled successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to cancel order: $e')),
          );
        }
      }
    }
  }

  Future<void> _partialCancelOrder(order_model.Order order) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        final selectedItemIndices = <int>[];
        final reasonController = TextEditingController();

        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Partial Cancel Order'),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text('Select items to cancel:'),
                    const SizedBox(height: 16),
                    Flexible(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: order.items.length,
                        itemBuilder: (context, index) {
                          final item = order.items[index];
                          final isSelected = selectedItemIndices.contains(index);

                          return CheckboxListTile(
                            title: Text(item.productName),
                            subtitle: Text('Qty: ${item.quantity} - ₹${item.price.toStringAsFixed(2)}'),
                            value: isSelected,
                            onChanged: (bool? value) {
                              setDialogState(() {
                                if (value == true) {
                                  selectedItemIndices.add(index);
                                } else {
                                  selectedItemIndices.remove(index);
                                }
                              });
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: reasonController,
                      decoration: const InputDecoration(
                        hintText: 'Cancellation reason',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: selectedItemIndices.isEmpty ? null : () {
                    if (reasonController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please provide a cancellation reason'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    Navigator.of(context).pop({
                      'itemIndices': List<int>.from(selectedItemIndices),
                      'reason': reasonController.text.trim(),
                    });
                  },
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: const Text('Partial Cancel'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result != null && result['itemIndices'] != null && result['reason'] != null) {
      final itemIndicesToCancel = result['itemIndices'] as List<int>;
      final reason = result['reason'] as String;

      if (itemIndicesToCancel.isNotEmpty && reason.isNotEmpty) {
        try {
          await _orderService.partialCancelOrderByIndices(order.id, itemIndicesToCancel, reason);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Items cancelled successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to cancel items: $e')),
            );
          }
        }
      }
    }
  }


}
