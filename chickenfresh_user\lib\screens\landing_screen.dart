import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../models/product_model.dart';
import '../models/combo_deal_model.dart';
import '../services/cart_provider.dart';
import '../services/location_service.dart';
import '../widgets/chicken_watermark.dart';
import 'cart_screen.dart';
import 'product_detail_screen.dart';
import 'notification_settings_screen.dart';
import 'profile_settings_screen.dart';

class LandingScreen extends StatefulWidget {
  const LandingScreen({super.key});

  @override
  State<LandingScreen> createState() => _LandingScreenState();
}

class _LandingScreenState extends State<LandingScreen> {
  final TextEditingController _searchController = TextEditingController();
  final PageController _bannerController = PageController();
  String _searchQuery = '';

  // Banner state
  List<Map<String, String>> _banners = [];
  int _currentBannerIndex = 0;

  bool _isLoading = true;
  String? _error;

  // Products for sidebar
  List<Product> _allProducts = [];
  bool _productsLoading = false;

  // Combo deals
  List<ComboDeal> _comboDeals = [];
  bool _comboDealsLoading = false;

  // Location state
  String _currentStreetName = 'Tap to get location';
  bool _locationLoading = false;

  // Scroll controller for hiding/showing app bar
  final ScrollController _scrollController = ScrollController();

  // Categories data with custom icons
  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'Today\'s Deals',
      'iconPath': 'assets/images/categories/deals.png',
      'fallbackIcon': Icons.local_offer,
      'color': Colors.red,
      'isSpecial': true,
      'id': 'deals',
    },
    {
      'name': 'Chicken',
      'iconPath': 'assets/images/categories/chicken.png',
      'fallbackIcon': Icons.restaurant_menu,
      'color': Colors.orange,
      'isSpecial': false,
      'id': 'chicken',
    },
    {
      'name': 'Boneless Chicken',
      'iconPath': 'assets/images/categories/boneless_chicken.png',
      'fallbackIcon': Icons.set_meal,
      'color': Colors.deepOrange,
      'isSpecial': false,
      'id': 'boneless_chicken',
    },
    {
      'name': 'Chicken Legs',
      'iconPath': 'assets/images/categories/chicken_legs.png',
      'fallbackIcon': Icons.dining,
      'color': Colors.orange,
      'isSpecial': false,
      'id': 'chicken_legs',
    },
    {
      'name': 'Chicken Breast',
      'iconPath': 'assets/images/categories/chicken_breast.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.amber,
      'isSpecial': false,
      'id': 'chicken_breast',
    },
    {
      'name': 'Chicken Wings',
      'iconPath': 'assets/images/categories/chicken_wings.png',
      'fallbackIcon': Icons.local_dining,
      'color': Colors.deepOrange,
      'isSpecial': false,
      'id': 'chicken_wings',
    },
    {
      'name': 'Whole Chicken',
      'iconPath': 'assets/images/categories/whole_chicken.png',
      'fallbackIcon': Icons.restaurant_menu,
      'color': Colors.orange,
      'isSpecial': false,
      'id': 'whole_chicken',
    },
    {
      'name': 'Fish & Seafood',
      'iconPath': 'assets/images/categories/fish.png',
      'fallbackIcon': Icons.set_meal,
      'color': Colors.blue,
      'isSpecial': false,
      'id': 'fish_seafood',
    },
    {
      'name': 'Mutton',
      'iconPath': 'assets/images/categories/mutton.png',
      'fallbackIcon': Icons.dining,
      'color': Colors.brown,
      'isSpecial': false,
      'id': 'mutton',
    },
    {
      'name': 'Ready to Cook',
      'iconPath': 'assets/images/categories/ready_to_cook.png',
      'fallbackIcon': Icons.microwave,
      'color': Colors.green,
      'isSpecial': false,
      'id': 'ready_to_cook',
    },
    {
      'name': 'Cold Cuts',
      'iconPath': 'assets/images/categories/cold_cuts.png',
      'fallbackIcon': Icons.kitchen,
      'color': Colors.purple,
      'isSpecial': false,
      'id': 'cold_cuts',
    },
    {
      'name': 'Plant based Meat',
      'iconPath': 'assets/images/categories/plant_based.png',
      'fallbackIcon': Icons.eco,
      'color': Colors.lightGreen,
      'isSpecial': false,
      'id': 'plant_based',
    },
    {
      'name': 'Spreads',
      'iconPath': 'assets/images/categories/spreads.png',
      'fallbackIcon': Icons.breakfast_dining,
      'color': Colors.amber,
      'isSpecial': false,
      'id': 'spreads',
    },
    {
      'name': 'Other',
      'iconPath': 'assets/images/categories/other.png',
      'fallbackIcon': Icons.more_horiz,
      'color': Colors.grey,
      'isSpecial': false,
      'id': 'other',
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
    _initializeBanners();
    _loadAllProducts();
    _loadComboDeals();
    _getCurrentLocation();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _bannerController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      await _fetchProducts();
    } catch (e) {
      setState(() {
        _error = 'Failed to load data: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAllProducts() async {
    try {
      setState(() {
        _productsLoading = true;
      });

      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('products')
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      final List<Product> products = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Product.fromFirestore(data, doc.id);
      }).toList();

      setState(() {
        _allProducts = products;
        _productsLoading = false;
      });
    } catch (e) {
      setState(() {
        _productsLoading = false;
      });
    }
  }

  Future<void> _loadComboDeals() async {
    try {
      setState(() {
        _comboDealsLoading = true;
      });

      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('combo_deals')
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final List<ComboDeal> comboDeals = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return ComboDeal.fromFirestore(data, doc.id);
      }).where((deal) => deal.isCurrentlyValid).toList();

      setState(() {
        _comboDeals = comboDeals;
        _comboDealsLoading = false;
      });
    } catch (e) {
      setState(() {
        _comboDealsLoading = false;
      });
      print('Error loading combo deals: $e');
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      setState(() {
        _locationLoading = true;
      });

      final streetName = await LocationService.getCurrentStreetName();

      setState(() {
        _currentStreetName = streetName;
        _locationLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentStreetName = 'Location unavailable';
        _locationLoading = false;
      });
    }
  }

  void _initializeBanners() {
    // Initialize with welcome banners - these will be replaced by admin-managed banners later
    _banners = [
      {
        'title': 'Welcome to ChickenFresh!',
        'subtitle': 'Fresh chicken delivered to your door',
        'image': 'assets/banner1.jpg', // placeholder
        'color': 'red',
      },
      {
        'title': 'Premium Quality',
        'subtitle': 'Farm-fresh chicken, guaranteed quality',
        'image': 'assets/banner2.jpg', // placeholder
        'color': 'green',
      },
      {
        'title': 'Fast Delivery',
        'subtitle': 'Quick delivery within 30 minutes',
        'image': 'assets/banner3.jpg', // placeholder
        'color': 'orange',
      },
    ];

    // Auto-scroll banners
    Timer.periodic(const Duration(seconds: 4), (timer) {
      if (mounted && _bannerController.hasClients) {
        setState(() {
          _currentBannerIndex = (_currentBannerIndex + 1) % _banners.length;
        });
        _bannerController.animateToPage(
          _currentBannerIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Future<void> _fetchProducts() async {
    final snapshot = await FirebaseFirestore.instance.collection('products').get();
    final products = snapshot.docs.map((doc) => Product.fromFirestore(doc.data(), doc.id)).toList();

    // Initialize product quantities
    for (var product in products) {
      product.quantity = 0; // Ensure quantity starts at 0
    }

    if (mounted) {
      Provider.of<CartProvider>(context, listen: false).clearCart();
      for (var product in products) {
        Provider.of<CartProvider>(context, listen: false).addProductToList(product);
      }
    }
  }



  List<Product> _getFilteredProducts(CartProvider cartProvider) {
    List<Product> filteredProducts = cartProvider.products;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredProducts = filteredProducts.where((product) {
        return product.name.toLowerCase().contains(_searchQuery) ||
               product.description.toLowerCase().contains(_searchQuery) ||
               product.category.toLowerCase().contains(_searchQuery);
      }).toList();
    }

    return filteredProducts;
  }

  Color _getBannerColor(String colorName) {
    switch (colorName) {
      case 'red':
        return Colors.red.shade600;
      case 'green':
        return Colors.green.shade600;
      case 'orange':
        return Colors.orange.shade600;
      case 'blue':
        return Colors.blue.shade600;
      case 'purple':
        return Colors.purple.shade600;
      default:
        return Colors.red.shade600;
    }
  }

  IconData _getBannerIcon(String colorName) {
    switch (colorName) {
      case 'red':
        return Icons.restaurant;
      case 'green':
        return Icons.eco;
      case 'orange':
        return Icons.delivery_dining;
      case 'blue':
        return Icons.star;
      case 'purple':
        return Icons.favorite;
      default:
        return Icons.restaurant;
    }
  }

  @override
  Widget build(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    final filteredProducts = _getFilteredProducts(cartProvider);

    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: Center(child: Text(_error!)),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              expandedHeight: 100.0,
              floating: true,
              pinned: false,
              snap: true,
              elevation: 4.0,
              leading: Builder(
                builder: (context) => GestureDetector(
                  onTap: () {
                    Scaffold.of(context).openDrawer();
                  },
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.asset(
                        'assets/images/chicken.jpg',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.restaurant_menu,
                            color: Colors.white,
                            size: 24,
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
              title: Row(
                children: [
                  // Location Icon and Street Name
                  GestureDetector(
                    onTap: _getCurrentLocation,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: Colors.white.withValues(alpha: 0.1),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _locationLoading
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(
                                  Icons.location_on,
                                  color: Colors.white,
                                  size: 16,
                                ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              _currentStreetName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const Spacer(),
                  // App Title
                  const Text(
                    'ChickenFresh',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.red.shade400,
                        Colors.red.shade600,
                        Colors.red.shade700,
                      ],
                    ),
                  ),
                ),
              ),
              actions: [
                // Cart Icon with Badge
                Stack(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.shopping_cart, color: Colors.white),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const CartScreen(),
                          ),
                        );
                      },
                    ),
                    if (cartProvider.products.where((p) => p.quantity > 0).isNotEmpty)
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade600,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            '${cartProvider.products.where((p) => p.quantity > 0).fold<int>(0, (total, item) => total + item.quantity)}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                // Notification Settings
                IconButton(
                  icon: const Icon(Icons.notifications, color: Colors.white),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationSettingsScreen(),
                      ),
                    );
                  },
                ),
                // Profile Settings
                IconButton(
                  icon: const Icon(Icons.person, color: Colors.white),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfileSettingsScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ];
        },
        body: ChickenImageWatermark(
        child: Column(
          children: [
            // Banner Section
            Container(
              height: 200,
              margin: const EdgeInsets.all(16.0),
              child: PageView.builder(
                controller: _bannerController,
                itemCount: _banners.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentBannerIndex = index;
                  });
                },
                itemBuilder: (context, index) {
                  final banner = _banners[index];
                  final color = _getBannerColor(banner['color']!);

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          color.withValues(alpha: 0.8),
                          color,
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.3),
                          spreadRadius: 2,
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background pattern
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              gradient: LinearGradient(
                                begin: Alignment.topRight,
                                end: Alignment.bottomLeft,
                                colors: [
                                  Colors.white.withValues(alpha: 0.1),
                                  Colors.transparent,
                                ],
                              ),
                            ),
                          ),
                        ),
                        // Content
                        Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _getBannerIcon(banner['color']!),
                                size: 48,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                banner['title']!,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                banner['subtitle']!,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Banner indicators
            Container(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _banners.asMap().entries.map((entry) {
                  return Container(
                    width: 8.0,
                    height: 8.0,
                    margin: const EdgeInsets.symmetric(horizontal: 4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentBannerIndex == entry.key
                          ? Colors.red.shade600
                          : Colors.grey.shade300,
                    ),
                  );
                }).toList(),
              ),
            ),

            // Search Section
            Container(
              margin: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 8.0),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search products...',
                    hintStyle: TextStyle(color: Colors.grey.shade500),
                    prefixIcon: Icon(Icons.search, color: Colors.green.shade600),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear, color: Colors.grey.shade600),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  ),
                ),
              ),
            ),

            // Categories Section
            Container(
              margin: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Shop by categories',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Freshest meats and much more!',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Categories Horizontal Scroll
                  SizedBox(
                    height: 100,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      itemCount: _categories.length,
                      itemBuilder: (context, index) {
                        final category = _categories[index];
                        return GestureDetector(
                          onTap: () {
                            // TODO: Filter products by category
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('${category['name']} selected'),
                                duration: const Duration(seconds: 1),
                              ),
                            );
                          },
                          child: Container(
                            width: 80,
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            child: Column(
                              children: [
                                Container(
                                  width: 64,
                                  height: 64,
                                  decoration: BoxDecoration(
                                    color: category['isSpecial']
                                        ? Colors.red.shade50
                                        : Colors.grey.shade50,
                                    borderRadius: BorderRadius.circular(32),
                                    border: category['isSpecial']
                                        ? Border.all(color: Colors.red.shade200, width: 2)
                                        : null,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        spreadRadius: 1,
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: category['isSpecial']
                                      ? Container(
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                Colors.red.shade400,
                                                Colors.red.shade600,
                                              ],
                                            ),
                                            borderRadius: BorderRadius.circular(32),
                                          ),
                                          child: Stack(
                                            children: [
                                              Center(
                                                child: ClipRRect(
                                                  borderRadius: BorderRadius.circular(32),
                                                  child: Image.asset(
                                                    category['iconPath'],
                                                    width: 40,
                                                    height: 40,
                                                    fit: BoxFit.cover,
                                                    color: Colors.white,
                                                    colorBlendMode: BlendMode.srcATop,
                                                    errorBuilder: (context, error, stackTrace) {
                                                      return Icon(
                                                        category['fallbackIcon'],
                                                        color: Colors.white,
                                                        size: 28,
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                top: 6,
                                                right: 6,
                                                child: Container(
                                                  width: 18,
                                                  height: 18,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius: BorderRadius.circular(9),
                                                  ),
                                                  child: Icon(
                                                    Icons.percent,
                                                    color: Colors.red.shade600,
                                                    size: 12,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      : Center(
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(32),
                                            child: Image.asset(
                                              category['iconPath'],
                                              width: 40,
                                              height: 40,
                                              fit: BoxFit.cover,
                                              errorBuilder: (context, error, stackTrace) {
                                                return Icon(
                                                  category['fallbackIcon'],
                                                  color: category['color'],
                                                  size: 28,
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  category['name'],
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Products Section
                    if (filteredProducts.isEmpty)
                      Container(
                        height: 300,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 64,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _searchQuery.isNotEmpty
                                    ? 'No products found'
                                    : 'No products available',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (_searchQuery.isNotEmpty) ...[
                                const SizedBox(height: 8),
                                Text(
                                  'Try adjusting your search',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      )
                    else
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.75,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemCount: filteredProducts.length,
                      itemBuilder: (context, index) {
                        final product = filteredProducts[index];
                        return GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ProductDetailScreen(product: product),
                              ),
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  spreadRadius: 1,
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Product Image
                                Expanded(
                                  flex: 3,
                                  child: Stack(
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        margin: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(12),
                                          color: Colors.grey.shade100,
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(12),
                                          child: product.imageUrl.isNotEmpty
                                              ? Image.network(
                                                  product.imageUrl,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error, stackTrace) {
                                                    return Center(
                                                      child: Icon(
                                                        Icons.restaurant,
                                                        size: 40,
                                                        color: Colors.grey.shade400,
                                                      ),
                                                    );
                                                  },
                                                )
                                              : Center(
                                                  child: Icon(
                                                    Icons.restaurant,
                                                    size: 40,
                                                    color: Colors.grey.shade400,
                                                  ),
                                                ),
                                        ),
                                      ),
                                      if (product.hasDiscount)
                                        Positioned(
                                          top: 12,
                                          right: 12,
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.red.shade600,
                                              borderRadius: BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              product.formattedDiscountPercentage,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),

                                // Product Details
                                Expanded(
                                  flex: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // Product Name
                                        Text(
                                          product.name,
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black87,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 4),

                                        // Price Row
                                        Row(
                                          children: [
                                            Text(
                                              product.formattedPrice,
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.green.shade700,
                                              ),
                                            ),
                                            if (product.hasDiscount) ...[
                                              const SizedBox(width: 4),
                                              Text(
                                                product.formattedMrp,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey.shade500,
                                                  decoration: TextDecoration.lineThrough,
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),

                                        const Spacer(),

                                        // Add to Cart Button
                                        SizedBox(
                                          width: double.infinity,
                                          height: 32,
                                          child: product.quantity > 0
                                              ? Container(
                                                  decoration: BoxDecoration(
                                                    color: Colors.green.shade50,
                                                    borderRadius: BorderRadius.circular(8),
                                                    border: Border.all(color: Colors.green.shade300),
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                    children: [
                                                      GestureDetector(
                                                        onTap: () => cartProvider.removeProduct(product),
                                                        child: Icon(
                                                          Icons.remove,
                                                          color: Colors.green.shade600,
                                                          size: 18,
                                                        ),
                                                      ),
                                                      Text(
                                                        product.quantity.toString(),
                                                        style: TextStyle(
                                                          fontSize: 14,
                                                          fontWeight: FontWeight.bold,
                                                          color: Colors.green.shade700,
                                                        ),
                                                      ),
                                                      GestureDetector(
                                                        onTap: () => cartProvider.addProduct(product),
                                                        child: Icon(
                                                          Icons.add,
                                                          color: Colors.green.shade600,
                                                          size: 18,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                )
                                              : ElevatedButton(
                                                  onPressed: () => cartProvider.addProduct(product),
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor: Colors.green.shade600,
                                                    foregroundColor: Colors.white,
                                                    elevation: 0,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius: BorderRadius.circular(8),
                                                    ),
                                                    padding: EdgeInsets.zero,
                                                  ),
                                                  child: const Text(
                                                    'ADD',
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    // Combo Deals Section
                    if (_comboDeals.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.local_offer,
                              color: Colors.red.shade600,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Combo Deals',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade700,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              'Limited Time',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.red.shade500,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        height: 200,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          itemCount: _comboDeals.length,
                          itemBuilder: (context, index) {
                            final comboDeal = _comboDeals[index];
                            return _buildComboDealCard(comboDeal);
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
        ),
      ),
      drawer: Drawer(
        child: Column(
          children: [
            // Drawer Header
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.red.shade400,
                    Colors.red.shade600,
                    Colors.red.shade700,
                  ],
                ),
              ),
              child: const SafeArea(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        'Products Menu',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'All available items',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Products List
            Expanded(
              child: _productsLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                      ),
                    )
                  : _allProducts.isEmpty
                      ? const Center(
                          child: Text(
                            'No products available',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          itemCount: _allProducts.length,
                          itemBuilder: (context, index) {
                            final product = _allProducts[index];
                            return ListTile(
                              leading: Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: Colors.red.shade50,
                                ),
                                child: Icon(
                                  Icons.restaurant_menu,
                                  color: Colors.red.shade400,
                                  size: 20,
                                ),
                              ),
                              title: Text(
                                product.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              subtitle: Text(
                                '₹${product.price.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.green.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              onTap: () {
                                Navigator.pop(context); // Close drawer
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => ProductDetailScreen(
                                      product: product,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComboDealCard(ComboDeal comboDeal) {
    return Container(
      width: 280,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Combo Deal Image
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                    color: Colors.grey.shade200,
                  ),
                  child: comboDeal.displayImageUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          child: Image.network(
                            comboDeal.displayImageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildComboFallbackImage();
                            },
                          ),
                        )
                      : _buildComboFallbackImage(),
                ),
                // Discount Badge
                if (comboDeal.hasDiscount)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red.shade600,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        comboDeal.formattedDiscountPercentage,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Combo Deal Details
          Expanded(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    comboDeal.name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    comboDeal.productsSummary,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            comboDeal.formattedComboPrice,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade600,
                            ),
                          ),
                          if (comboDeal.hasDiscount)
                            Text(
                              comboDeal.formattedOriginalPrice,
                              style: TextStyle(
                                fontSize: 12,
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey.shade500,
                              ),
                            ),
                        ],
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: () {
                          final cartProvider = Provider.of<CartProvider>(context, listen: false);
                          cartProvider.addComboDeal(comboDeal);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('${comboDeal.name} added to cart!'),
                              backgroundColor: Colors.green,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade600,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        ),
                        child: const Text(
                          'ADD',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComboFallbackImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red.shade100,
            Colors.orange.shade100,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_offer,
            size: 32,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 4),
          Text(
            'Combo Deal',
            style: TextStyle(
              fontSize: 12,
              color: Colors.red.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}