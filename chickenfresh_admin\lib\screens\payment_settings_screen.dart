import 'package:flutter/material.dart';
import '../models/payment_config_model.dart';
import '../services/payment_config_service.dart';

class PaymentSettingsScreen extends StatefulWidget {
  const PaymentSettingsScreen({super.key});

  @override
  State<PaymentSettingsScreen> createState() => _PaymentSettingsScreenState();
}

class _PaymentSettingsScreenState extends State<PaymentSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PaymentConfigModel? _config;
  bool _isLoading = true;
  bool _isSaving = false;

  // PhonePe Controllers
  final _phonePeMerchantIdController = TextEditingController();
  final _phonePeSaltKeyController = TextEditingController();
  final _phonePeSaltIndexController = TextEditingController();
  final _phonePeBaseUrlController = TextEditingController();

  // Razorpay Controllers
  final _razorpayKeyIdController = TextEditingController();
  final _razorpayKeySecretController = TextEditingController();

  // UPI Controllers
  final _upiMerchantIdController = TextEditingController();
  final _upiMerchantNameController = TextEditingController();

  // COD Controllers
  final _codMinAmountController = TextEditingController();
  final _codMaxAmountController = TextEditingController();
  final _codChargesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadPaymentConfig();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phonePeMerchantIdController.dispose();
    _phonePeSaltKeyController.dispose();
    _phonePeSaltIndexController.dispose();
    _phonePeBaseUrlController.dispose();
    _razorpayKeyIdController.dispose();
    _razorpayKeySecretController.dispose();
    _upiMerchantIdController.dispose();
    _upiMerchantNameController.dispose();
    _codMinAmountController.dispose();
    _codMaxAmountController.dispose();
    _codChargesController.dispose();
    super.dispose();
  }

  Future<void> _loadPaymentConfig() async {
    try {
      final config = await PaymentConfigService.getPaymentConfig();
      setState(() {
        _config = config;
        _isLoading = false;
      });
      _populateControllers();
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load payment configuration: $e');
    }
  }

  void _populateControllers() {
    if (_config == null) return;

    // PhonePe
    _phonePeMerchantIdController.text = _config!.phonePeMerchantId;
    _phonePeSaltKeyController.text = _config!.phonePeSaltKey;
    _phonePeSaltIndexController.text = _config!.phonePeSaltIndex.toString();
    _phonePeBaseUrlController.text = _config!.phonePeBaseUrl;

    // Razorpay
    _razorpayKeyIdController.text = _config!.razorpayKeyId;
    _razorpayKeySecretController.text = _config!.razorpayKeySecret;

    // UPI
    _upiMerchantIdController.text = _config!.upiMerchantId;
    _upiMerchantNameController.text = _config!.upiMerchantName;

    // COD
    _codMinAmountController.text = _config!.codMinAmount.toString();
    _codMaxAmountController.text = _config!.codMaxAmount.toString();
    _codChargesController.text = _config!.codCharges.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Settings'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        bottom: _isLoading
            ? null
            : TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                indicatorColor: Colors.white,
                tabs: const [
                  Tab(text: 'Overview'),
                  Tab(text: 'PhonePe'),
                  Tab(text: 'Razorpay'),
                  Tab(text: 'UPI'),
                  Tab(text: 'COD'),
                ],
              ),
        actions: [
          if (!_isLoading && _config != null)
            IconButton(
              icon: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.save),
              onPressed: _isSaving ? null : _saveAllSettings,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _config == null
              ? const Center(child: Text('Failed to load configuration'))
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildPhonePeTab(),
                    _buildRazorpayTab(),
                    _buildUpiTab(),
                    _buildCodTab(),
                  ],
                ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _config!.isPaymentEnabled ? Icons.check_circle : Icons.cancel,
                        color: _config!.isPaymentEnabled ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Payment System',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Switch(
                        value: _config!.isPaymentEnabled,
                        onChanged: (value) async {
                          await PaymentConfigService.togglePayments(value);
                          _loadPaymentConfig();
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _config!.isPaymentEnabled
                        ? 'Payment system is active'
                        : 'Payment system is disabled',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Payment Methods Status
          const Text(
            'Payment Methods',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          _buildPaymentMethodCard(
            'PhonePe',
            _config!.isPhonePeEnabled,
            _config!.hasValidPhonePeConfig,
            Icons.phone_android,
            Colors.purple,
            () => _togglePaymentMethod('phonepe'),
          ),
          
          _buildPaymentMethodCard(
            'Razorpay',
            _config!.isRazorpayEnabled,
            _config!.hasValidRazorpayConfig,
            Icons.payment,
            Colors.blue,
            () => _togglePaymentMethod('razorpay'),
          ),
          
          _buildPaymentMethodCard(
            'UPI',
            _config!.isUpiEnabled,
            true, // UPI is always valid if enabled
            Icons.account_balance,
            Colors.teal,
            () => _togglePaymentMethod('upi'),
          ),
          
          _buildPaymentMethodCard(
            'Cash on Delivery',
            _config!.isCodEnabled,
            true, // COD is always valid if enabled
            Icons.money,
            Colors.green,
            () => _togglePaymentMethod('cod'),
          ),

          const SizedBox(height: 24),

          // Default Payment Method
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Default Payment Method',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  DropdownButtonFormField<String>(
                    value: _config!.defaultPaymentMethod,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'Default Method',
                    ),
                    items: _config!.enabledPaymentMethods.map((method) {
                      return DropdownMenuItem(
                        value: method,
                        child: Text(method.toUpperCase()),
                      );
                    }).toList(),
                    onChanged: (value) async {
                      if (value != null) {
                        await PaymentConfigService.setDefaultPaymentMethod(value);
                        _loadPaymentConfig();
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodCard(
    String name,
    bool isEnabled,
    bool isConfigured,
    IconData icon,
    Color color,
    VoidCallback onToggle,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(name),
        subtitle: Text(
          isEnabled
              ? (isConfigured ? 'Active & Configured' : 'Enabled but not configured')
              : 'Disabled',
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isEnabled && !isConfigured)
              Icon(Icons.warning, color: Colors.orange, size: 20),
            const SizedBox(width: 8),
            Switch(
              value: isEnabled,
              onChanged: (_) => onToggle(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhonePeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enable/Disable Switch
          Card(
            child: SwitchListTile(
              title: const Text('Enable PhonePe'),
              subtitle: const Text('Accept payments through PhonePe'),
              value: _config!.isPhonePeEnabled,
              onChanged: (value) async {
                await PaymentConfigService.togglePaymentMethod('phonepe', value);
                _loadPaymentConfig();
              },
            ),
          ),
          const SizedBox(height: 16),

          // Configuration Fields
          if (_config!.isPhonePeEnabled) ...[
            const Text(
              'PhonePe Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Test Mode Switch
            Card(
              child: SwitchListTile(
                title: const Text('Test Mode'),
                subtitle: const Text('Use sandbox environment for testing'),
                value: _config!.isPhonePeTestMode,
                onChanged: (value) {
                  setState(() {
                    _config = _config!.copyWith(isPhonePeTestMode: value);
                  });
                },
              ),
            ),
            const SizedBox(height: 16),

            // Merchant ID
            TextFormField(
              controller: _phonePeMerchantIdController,
              decoration: const InputDecoration(
                labelText: 'Merchant ID',
                border: OutlineInputBorder(),
                helperText: 'Your PhonePe merchant ID',
              ),
            ),
            const SizedBox(height: 16),

            // Salt Key
            TextFormField(
              controller: _phonePeSaltKeyController,
              decoration: const InputDecoration(
                labelText: 'Salt Key',
                border: OutlineInputBorder(),
                helperText: 'Your PhonePe salt key',
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),

            // Salt Index
            TextFormField(
              controller: _phonePeSaltIndexController,
              decoration: const InputDecoration(
                labelText: 'Salt Index',
                border: OutlineInputBorder(),
                helperText: 'Salt index (usually 1)',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),

            // Base URL
            TextFormField(
              controller: _phonePeBaseUrlController,
              decoration: const InputDecoration(
                labelText: 'Base URL',
                border: OutlineInputBorder(),
                helperText: 'PhonePe API base URL',
              ),
            ),
            const SizedBox(height: 24),

            // Test Configuration Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _testConfiguration('phonepe'),
                icon: const Icon(Icons.science),
                label: const Text('Test Configuration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRazorpayTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enable/Disable Switch
          Card(
            child: SwitchListTile(
              title: const Text('Enable Razorpay'),
              subtitle: const Text('Accept payments through Razorpay'),
              value: _config!.isRazorpayEnabled,
              onChanged: (value) async {
                await PaymentConfigService.togglePaymentMethod('razorpay', value);
                _loadPaymentConfig();
              },
            ),
          ),
          const SizedBox(height: 16),

          if (_config!.isRazorpayEnabled) ...[
            const Text(
              'Razorpay Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Test Mode Switch
            Card(
              child: SwitchListTile(
                title: const Text('Test Mode'),
                subtitle: const Text('Use test keys for development'),
                value: _config!.isRazorpayTestMode,
                onChanged: (value) {
                  setState(() {
                    _config = _config!.copyWith(isRazorpayTestMode: value);
                  });
                },
              ),
            ),
            const SizedBox(height: 16),

            // Key ID
            TextFormField(
              controller: _razorpayKeyIdController,
              decoration: const InputDecoration(
                labelText: 'Key ID',
                border: OutlineInputBorder(),
                helperText: 'Your Razorpay key ID',
              ),
            ),
            const SizedBox(height: 16),

            // Key Secret
            TextFormField(
              controller: _razorpayKeySecretController,
              decoration: const InputDecoration(
                labelText: 'Key Secret',
                border: OutlineInputBorder(),
                helperText: 'Your Razorpay key secret',
              ),
              obscureText: true,
            ),
            const SizedBox(height: 24),

            // Test Configuration Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _testConfiguration('razorpay'),
                icon: const Icon(Icons.science),
                label: const Text('Test Configuration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildUpiTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enable/Disable Switch
          Card(
            child: SwitchListTile(
              title: const Text('Enable UPI'),
              subtitle: const Text('Accept payments through UPI apps'),
              value: _config!.isUpiEnabled,
              onChanged: (value) async {
                await PaymentConfigService.togglePaymentMethod('upi', value);
                _loadPaymentConfig();
              },
            ),
          ),
          const SizedBox(height: 16),

          if (_config!.isUpiEnabled) ...[
            const Text(
              'UPI Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Merchant UPI ID
            TextFormField(
              controller: _upiMerchantIdController,
              decoration: const InputDecoration(
                labelText: 'Merchant UPI ID',
                border: OutlineInputBorder(),
                helperText: 'Your UPI ID for receiving payments',
              ),
            ),
            const SizedBox(height: 16),

            // Merchant Name
            TextFormField(
              controller: _upiMerchantNameController,
              decoration: const InputDecoration(
                labelText: 'Merchant Name',
                border: OutlineInputBorder(),
                helperText: 'Business name shown in UPI apps',
              ),
            ),
            const SizedBox(height: 24),

            // Test Configuration Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _testConfiguration('upi'),
                icon: const Icon(Icons.science),
                label: const Text('Test Configuration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCodTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enable/Disable Switch
          Card(
            child: SwitchListTile(
              title: const Text('Enable Cash on Delivery'),
              subtitle: const Text('Accept cash payments on delivery'),
              value: _config!.isCodEnabled,
              onChanged: (value) async {
                await PaymentConfigService.togglePaymentMethod('cod', value);
                _loadPaymentConfig();
              },
            ),
          ),
          const SizedBox(height: 16),

          if (_config!.isCodEnabled) ...[
            const Text(
              'COD Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Minimum Amount
            TextFormField(
              controller: _codMinAmountController,
              decoration: const InputDecoration(
                labelText: 'Minimum Order Amount',
                border: OutlineInputBorder(),
                helperText: 'Minimum amount for COD orders',
                prefixText: '₹ ',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),

            // Maximum Amount
            TextFormField(
              controller: _codMaxAmountController,
              decoration: const InputDecoration(
                labelText: 'Maximum Order Amount',
                border: OutlineInputBorder(),
                helperText: 'Maximum amount for COD orders',
                prefixText: '₹ ',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),

            // COD Charges
            TextFormField(
              controller: _codChargesController,
              decoration: const InputDecoration(
                labelText: 'COD Charges',
                border: OutlineInputBorder(),
                helperText: 'Additional charges for COD (0 for free)',
                prefixText: '₹ ',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _togglePaymentMethod(String method) async {
    try {
      bool currentValue = false;
      switch (method) {
        case 'phonepe':
          currentValue = _config!.isPhonePeEnabled;
          break;
        case 'razorpay':
          currentValue = _config!.isRazorpayEnabled;
          break;
        case 'upi':
          currentValue = _config!.isUpiEnabled;
          break;
        case 'cod':
          currentValue = _config!.isCodEnabled;
          break;
      }

      await PaymentConfigService.togglePaymentMethod(method, !currentValue);
      _loadPaymentConfig();
    } catch (e) {
      _showErrorSnackBar('Failed to toggle payment method: $e');
    }
  }

  Future<void> _testConfiguration(String method) async {
    try {
      final result = await PaymentConfigService.testPaymentConfig(method);
      
      if (result['success']) {
        _showSuccessSnackBar(result['message']);
      } else {
        _showErrorSnackBar(result['error']);
      }
    } catch (e) {
      _showErrorSnackBar('Test failed: $e');
    }
  }

  Future<void> _saveAllSettings() async {
    setState(() => _isSaving = true);
    
    try {
      final updatedConfig = _config!.copyWith(
        // PhonePe
        phonePeMerchantId: _phonePeMerchantIdController.text.trim(),
        phonePeSaltKey: _phonePeSaltKeyController.text.trim(),
        phonePeSaltIndex: int.tryParse(_phonePeSaltIndexController.text) ?? 1,
        phonePeBaseUrl: _phonePeBaseUrlController.text.trim(),
        
        // Razorpay
        razorpayKeyId: _razorpayKeyIdController.text.trim(),
        razorpayKeySecret: _razorpayKeySecretController.text.trim(),
        
        // UPI
        upiMerchantId: _upiMerchantIdController.text.trim(),
        upiMerchantName: _upiMerchantNameController.text.trim(),
        
        // COD
        codMinAmount: double.tryParse(_codMinAmountController.text) ?? 0.0,
        codMaxAmount: double.tryParse(_codMaxAmountController.text) ?? 5000.0,
        codCharges: double.tryParse(_codChargesController.text) ?? 0.0,
        
        updatedAt: DateTime.now(),
      );

      await PaymentConfigService.updatePaymentConfig(updatedConfig);
      _showSuccessSnackBar('Payment settings saved successfully');
      _loadPaymentConfig();
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    } finally {
      setState(() => _isSaving = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
