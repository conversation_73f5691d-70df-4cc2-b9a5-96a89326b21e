import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class LocationService {
  static Position? _currentPosition;
  static String? _currentStreetName;
  static bool _isLocationEnabled = false;

  // Get current location and street name
  static Future<String> getCurrentStreetName() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (kDebugMode) {
          print('Location services are disabled.');
        }
        return 'Location services disabled';
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (kDebugMode) {
            print('Location permissions are denied');
          }
          return 'Location permission denied';
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (kDebugMode) {
          print('Location permissions are permanently denied');
        }
        return 'Location permission denied';
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      _currentPosition = position;
      _isLocationEnabled = true;

      if (kDebugMode) {
        print('Current position: ${position.latitude}, ${position.longitude}');
      }

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        
        // Try to get the most specific street information
        String streetName = '';
        
        if (place.street != null && place.street!.isNotEmpty) {
          streetName = place.street!;
        } else if (place.thoroughfare != null && place.thoroughfare!.isNotEmpty) {
          streetName = place.thoroughfare!;
        } else if (place.subThoroughfare != null && place.subThoroughfare!.isNotEmpty) {
          streetName = place.subThoroughfare!;
        } else if (place.locality != null && place.locality!.isNotEmpty) {
          streetName = place.locality!;
        } else if (place.subLocality != null && place.subLocality!.isNotEmpty) {
          streetName = place.subLocality!;
        } else {
          streetName = 'Unknown location';
        }

        _currentStreetName = streetName;

        if (kDebugMode) {
          print('Street name: $streetName');
          print('Full address: ${place.street}, ${place.locality}, ${place.administrativeArea}');
        }

        return streetName;
      } else {
        if (kDebugMode) {
          print('No placemarks found');
        }
        return 'Unknown location';
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting location: $e');
      }
      
      // Return cached street name if available
      if (_currentStreetName != null) {
        return _currentStreetName!;
      }
      
      return 'Location unavailable';
    }
  }

  // Get cached street name without making a new request
  static String getCachedStreetName() {
    return _currentStreetName ?? 'Tap to get location';
  }

  // Check if location is enabled
  static bool isLocationEnabled() {
    return _isLocationEnabled;
  }

  // Get current position
  static Position? getCurrentPosition() {
    return _currentPosition;
  }

  // Request location permission
  static Future<bool> requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      
      return permission == LocationPermission.whileInUse || 
             permission == LocationPermission.always;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting location permission: $e');
      }
      return false;
    }
  }

  // Open location settings
  static Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      if (kDebugMode) {
        print('Error opening location settings: $e');
      }
    }
  }

  // Open app settings
  static Future<void> openAppSettings() async {
    try {
      await Geolocator.openAppSettings();
    } catch (e) {
      if (kDebugMode) {
        print('Error opening app settings: $e');
      }
    }
  }

  // Clear cached location data
  static void clearCache() {
    _currentPosition = null;
    _currentStreetName = null;
    _isLocationEnabled = false;
  }

  // Get distance between two points
  static double getDistanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }
}
