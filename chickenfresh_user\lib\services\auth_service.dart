import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class AuthException implements Exception {
  final String message;
  AuthException(this.message);
}

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Rate limiting - more lenient
  final Map<String, DateTime> _lastAttempts = {};
  static const int maxAttemptsPerMinute = 10;
  static const Duration rateLimitWindow = Duration(minutes: 2);

  // Current user stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();
  
  // Get current user
  User? get currentUser => _auth.currentUser;
  
  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Sign up with email and password
  Future<UserCredential> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      if (kDebugMode) {
        print('Creating user account for: $email');
        print('Firebase Auth instance: ${_auth.app.name}');
        print('Project ID: ${_auth.app.options.projectId}');
      }

      // Input validation
      _validateEmail(email);
      _validatePassword(password);
      _validateName(fullName);

      // Rate limiting
      await _checkRateLimit(email);

      // Create user account
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim().toLowerCase(),
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(fullName.trim());

        // Create user document in Firestore
        await _createUserDocument(credential.user!, fullName.trim());

        if (kDebugMode) {
          print('User created successfully: ${credential.user?.email}');
        }
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('FirebaseAuthException during user creation:');
        print('Code: ${e.code}');
        print('Message: ${e.message}');
        print('Plugin: ${e.plugin}');
      }
      throw _handleAuthException(e);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error during user creation: $e');
      }
      throw AuthException('An unexpected error occurred: $e');
    }
  }

  // Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // Input validation
      _validateEmail(email);
      _validatePassword(password);
      
      // Rate limiting
      await _checkRateLimit(email);
      
      // Attempt sign in
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim().toLowerCase(),
        password: password,
      );

      if (kDebugMode) {
        print('User signed in successfully: ${credential.user?.email}');
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw AuthException('An unexpected error occurred: $e');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw AuthException('Failed to sign out: $e');
    }
  }

  // Reset password
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      _validateEmail(email);
      await _auth.sendPasswordResetEmail(email: email.trim().toLowerCase());
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw AuthException('Failed to send password reset email: $e');
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument(User user, String fullName) async {
    try {
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'email': user.email,
        'fullName': fullName,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'isActive': true,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to create user document: $e');
      }
      // Don't throw here as the user account was created successfully
    }
  }

  // Input validation methods
  void _validateEmail(String email) {
    if (email.isEmpty) {
      throw AuthException('Email is required');
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      throw AuthException('Please enter a valid email address');
    }
  }

  void _validatePassword(String password) {
    if (password.isEmpty) {
      throw AuthException('Password is required');
    }
    if (password.length < 6) {
      throw AuthException('Password must be at least 6 characters long');
    }
  }

  void _validateName(String name) {
    if (name.trim().isEmpty) {
      throw AuthException('Full name is required');
    }
    if (name.trim().length < 2) {
      throw AuthException('Full name must be at least 2 characters long');
    }
  }

  // Rate limiting
  Future<void> _checkRateLimit(String email) async {
    final now = DateTime.now();
    final lastAttempt = _lastAttempts[email];

    if (lastAttempt != null &&
        now.difference(lastAttempt) < rateLimitWindow) {
      final remainingTime = rateLimitWindow - now.difference(lastAttempt);
      throw AuthException('Too many attempts. Please wait ${remainingTime.inMinutes + 1} minutes before trying again.');
    }

    _lastAttempts[email] = now;
  }

  // Handle Firebase Auth exceptions
  AuthException _handleAuthException(FirebaseAuthException e) {
    if (kDebugMode) {
      print('Firebase Auth Error - Code: ${e.code}, Message: ${e.message}');
    }

    switch (e.code) {
      case 'user-not-found':
        return AuthException('No account found with this email address');
      case 'wrong-password':
        return AuthException('Incorrect password');
      case 'email-already-in-use':
        return AuthException('An account already exists with this email address');
      case 'weak-password':
        return AuthException('Password is too weak (minimum 6 characters)');
      case 'invalid-email':
        return AuthException('Invalid email address');
      case 'user-disabled':
        return AuthException('This account has been disabled');
      case 'too-many-requests':
        return AuthException('Too many failed attempts. Please try again later');
      case 'network-request-failed':
        return AuthException('Network error. Please check your connection');
      case 'invalid-credential':
        return AuthException('Invalid email or password');
      case 'operation-not-allowed':
        return AuthException('Email/password accounts are not enabled. Please contact support');
      case 'configuration-not-found':
        return AuthException('Firebase configuration error. Please contact support');
      case 'app-not-authorized':
        return AuthException('App not authorized to use Firebase Authentication');
      default:
        return AuthException('Authentication failed: ${e.code} - ${e.message}');
    }
  }
}
