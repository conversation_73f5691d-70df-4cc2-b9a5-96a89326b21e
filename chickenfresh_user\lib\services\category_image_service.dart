import 'package:flutter/material.dart';

/// Service to handle category images and provide fallback images for products
class CategoryImageService {
  // Map of category names to their image paths and fallback icons
  static const Map<String, Map<String, dynamic>> _categoryData = {
    'chicken': {
      'imagePath': 'assets/images/categories/chicken.png',
      'fallbackIcon': Icons.restaurant_menu,
      'color': Colors.orange,
    },
    'boneless chicken': {
      'imagePath': 'assets/images/categories/boneless_chicken.png',
      'fallbackIcon': Icons.set_meal,
      'color': Colors.deepOrange,
    },
    'chicken legs': {
      'imagePath': 'assets/images/categories/chicken_legs.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.brown,
    },
    'chicken breast': {
      'imagePath': 'assets/images/categories/chicken_breast.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.pink,
    },
    'chicken wings': {
      'imagePath': 'assets/images/categories/chicken_wings.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.amber,
    },
    'whole chicken': {
      'imagePath': 'assets/images/categories/whole_chicken.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.orange,
    },
    'fish': {
      'imagePath': 'assets/images/categories/fish.png',
      'fallbackIcon': Icons.set_meal,
      'color': Colors.blue,
    },
    'mutton': {
      'imagePath': 'assets/images/categories/mutton.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.red,
    },
    'ready to cook': {
      'imagePath': 'assets/images/categories/ready_to_cook.png',
      'fallbackIcon': Icons.microwave,
      'color': Colors.green,
    },
    'cold cuts': {
      'imagePath': 'assets/images/categories/cold_cuts.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.purple,
    },
    'plant based': {
      'imagePath': 'assets/images/categories/plant_based.png',
      'fallbackIcon': Icons.eco,
      'color': Colors.green,
    },
    'spreads': {
      'imagePath': 'assets/images/categories/spreads.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.yellow,
    },
    'other': {
      'imagePath': 'assets/images/categories/other.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.grey,
    },
    'general': {
      'imagePath': 'assets/images/categories/other.png',
      'fallbackIcon': Icons.restaurant,
      'color': Colors.grey,
    },
  };

  /// Get category image path for a given category name
  static String? getCategoryImagePath(String category) {
    final normalizedCategory = category.toLowerCase().trim();
    return _categoryData[normalizedCategory]?['imagePath'];
  }

  /// Get fallback icon for a given category name
  static IconData getCategoryFallbackIcon(String category) {
    final normalizedCategory = category.toLowerCase().trim();
    return _categoryData[normalizedCategory]?['fallbackIcon'] ?? Icons.restaurant;
  }

  /// Get category color for a given category name
  static Color getCategoryColor(String category) {
    final normalizedCategory = category.toLowerCase().trim();
    return _categoryData[normalizedCategory]?['color'] ?? Colors.grey;
  }

  /// Check if a category has a defined image
  static bool hasCategoryImage(String category) {
    final normalizedCategory = category.toLowerCase().trim();
    return _categoryData.containsKey(normalizedCategory);
  }

  /// Get all available categories
  static List<String> getAllCategories() {
    return _categoryData.keys.toList();
  }

  /// Build a widget that displays either product image or category fallback
  static Widget buildProductImage({
    required String productImageUrl,
    required String category,
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Widget? errorWidget,
  }) {
    final categoryImagePath = getCategoryImagePath(category);
    final fallbackIcon = getCategoryFallbackIcon(category);
    final categoryColor = getCategoryColor(category);

    Widget imageWidget;

    if (productImageUrl.isNotEmpty) {
      // Try to load product image first
      imageWidget = Image.network(
        productImageUrl,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          // If product image fails, try category image
          if (categoryImagePath != null) {
            return Image.asset(
              categoryImagePath,
              width: width,
              height: height,
              fit: fit,
              errorBuilder: (context, error, stackTrace) {
                // If category image also fails, show icon
                return errorWidget ?? _buildFallbackIcon(fallbackIcon, categoryColor, width, height);
              },
            );
          } else {
            // No category image, show icon directly
            return errorWidget ?? _buildFallbackIcon(fallbackIcon, categoryColor, width, height);
          }
        },
      );
    } else {
      // No product image, try category image
      if (categoryImagePath != null) {
        imageWidget = Image.asset(
          categoryImagePath,
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            // If category image fails, show icon
            return errorWidget ?? _buildFallbackIcon(fallbackIcon, categoryColor, width, height);
          },
        );
      } else {
        // No category image, show icon directly
        imageWidget = errorWidget ?? _buildFallbackIcon(fallbackIcon, categoryColor, width, height);
      }
    }

    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Build fallback icon widget
  static Widget _buildFallbackIcon(IconData icon, Color color, double width, double height) {
    return Container(
      width: width,
      height: height,
      color: color.withValues(alpha: 0.1),
      child: Icon(
        icon,
        color: color,
        size: (width + height) / 4, // Responsive icon size
      ),
    );
  }
}
