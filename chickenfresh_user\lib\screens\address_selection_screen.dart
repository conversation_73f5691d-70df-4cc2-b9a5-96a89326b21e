import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/user_profile_service.dart';
import '../widgets/chicken_watermark.dart';
import 'profile_edit_screen.dart';

class AddressSelectionScreen extends StatefulWidget {
  final Function(String apartmentName, String blockName, String houseNumber, UserProfile? profile) onAddressSelected;

  const AddressSelectionScreen({super.key, required this.onAddressSelected});

  @override
  State<AddressSelectionScreen> createState() => _AddressSelectionScreenState();
}

class _AddressSelectionScreenState extends State<AddressSelectionScreen> {
  String? _apartmentName;
  String? _selectedBlockName;
  String? _selectedHouseNumber;
  List<String> _availableBlockNames = [];
  Map<String, List<String>> _blockHouseNumbersMap = {};
  List<String> _currentHouseNumbers = [];
  UserProfile? _currentUserProfile;
  
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAddressData();
  }

  Future<void> _loadAddressData() async {
    final prefs = await SharedPreferences.getInstance();
    final savedApartment = prefs.getString('apartment');
    final registeredBlocksJson = prefs.getString('registeredBlocks');

    if (savedApartment != null && registeredBlocksJson != null) {
      try {
        final registeredBlocks = Map<String, List<String>>.from(
          (prefs.getString('registeredBlocks') != null)
              ? Map<String, dynamic>.from(
                  Map<String, dynamic>.from(
                    Map<String, dynamic>.from(
                      prefs.getString('registeredBlocks')!.split(',').asMap().map(
                        (index, value) => MapEntry(value, []),
                      ),
                    ),
                  ),
                )
              : {},
        );

        setState(() {
          _apartmentName = savedApartment;
          _blockHouseNumbersMap = registeredBlocks;
          _availableBlockNames = registeredBlocks.keys.toList()..sort();
        });

        // Load saved selections
        final savedBlock = prefs.getString('selectedBlock');
        final savedHouse = prefs.getString('selectedHouse');

        if (savedBlock != null && _availableBlockNames.contains(savedBlock)) {
          _onBlockChanged(savedBlock);
          if (savedHouse != null) {
            _onHouseNumberChanged(savedHouse);
          }
        }
      } catch (e) {
        setState(() {
          _error = 'Error loading address data: $e';
        });
      }
    } else {
      setState(() {
        _error = 'No address data found. Please register first.';
      });
    }
    
    setState(() {
      _isLoading = false;
    });
  }

  void _onBlockChanged(String? blockName) async {
    if (blockName == null) return;

    setState(() {
      _selectedBlockName = blockName;
      _currentHouseNumbers = _blockHouseNumbersMap[blockName] ?? [];
      _selectedHouseNumber = null;
      _currentUserProfile = null;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selectedBlock', blockName);
  }

  void _onHouseNumberChanged(String? houseNumber) async {
    if (houseNumber == null || _selectedBlockName == null) return;

    setState(() {
      _selectedHouseNumber = houseNumber;
    });

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selectedHouse', houseNumber);

    // Load user profile for this address
    try {
      final userProfile = await UserProfileService.getUserProfile(
        apartmentName: _apartmentName!,
        blockName: _selectedBlockName!,
        houseNumber: houseNumber,
      );
      setState(() {
        _currentUserProfile = userProfile;
      });
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'Select Address',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.red.shade400,
                  Colors.red.shade600,
                  Colors.red.shade700,
                ],
              ),
            ),
          ),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'Select Address',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.red.shade400,
                  Colors.red.shade600,
                  Colors.red.shade700,
                ],
              ),
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
              const SizedBox(height: 16),
              Text(
                _error!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Select Delivery Address',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.red.shade400,
                Colors.red.shade600,
                Colors.red.shade700,
              ],
            ),
          ),
        ),
      ),
      body: ChickenImageWatermark(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Apartment Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade50, Colors.blue.shade100],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.apartment, color: Colors.blue.shade600, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Apartment Complex',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            _apartmentName ?? 'Unknown',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Block Selection
              const Text(
                'Select Block',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedBlockName,
                  hint: const Text('Choose your block'),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    prefixIcon: Icon(Icons.location_city),
                  ),
                  items: _availableBlockNames.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: _onBlockChanged,
                ),
              ),
              const SizedBox(height: 20),

              // House Number Selection
              const Text(
                'Select House Number',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedHouseNumber,
                  hint: const Text('Choose your house number'),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    prefixIcon: Icon(Icons.home),
                  ),
                  items: _currentHouseNumbers.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: _onHouseNumberChanged,
                ),
              ),
              const SizedBox(height: 24),

              // User Profile Info
              if (_currentUserProfile != null) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.green.shade50, Colors.green.shade100],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.person, color: Colors.green.shade600),
                          const SizedBox(width: 8),
                          const Text(
                            'Delivery Contact',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Name: ${_currentUserProfile!.name}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      Text(
                        'Phone: ${_currentUserProfile!.phoneNumber}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 12),
                      TextButton.icon(
                        onPressed: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ProfileEditScreen(
                                apartmentName: _apartmentName!,
                                blockName: _selectedBlockName!,
                                houseNumber: _selectedHouseNumber!,
                                currentProfile: _currentUserProfile,
                                onProfileUpdated: () {
                                  _onHouseNumberChanged(_selectedHouseNumber);
                                  Navigator.of(context).pop();
                                },
                              ),
                            ),
                          );
                        },
                        icon: const Icon(Icons.edit, size: 16),
                        label: const Text('Edit Details'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],

              const Spacer(),

              // Confirm Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _selectedBlockName != null && _selectedHouseNumber != null
                      ? () {
                          widget.onAddressSelected(
                            _apartmentName!,
                            _selectedBlockName!,
                            _selectedHouseNumber!,
                            _currentUserProfile,
                          );
                          Navigator.of(context).pop();
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade400,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Confirm Address',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
