import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import '../lib/services/category_image_service.dart';

void main() {
  group('CategoryImageService Tests', () {
    test('should return correct image path for known categories', () {
      expect(
        CategoryImageService.getCategoryImagePath('chicken'),
        'assets/images/categories/chicken.png',
      );
      
      expect(
        CategoryImageService.getCategoryImagePath('boneless chicken'),
        'assets/images/categories/boneless_chicken.png',
      );
      
      expect(
        CategoryImageService.getCategoryImagePath('fish'),
        'assets/images/categories/fish.png',
      );
    });

    test('should return null for unknown categories', () {
      expect(
        CategoryImageService.getCategoryImagePath('unknown_category'),
        null,
      );
    });

    test('should handle case insensitive category names', () {
      expect(
        CategoryImageService.getCategoryImagePath('CHICKEN'),
        'assets/images/categories/chicken.png',
      );
      
      expect(
        CategoryImageService.getCategoryImagePath('Boneless Chicken'),
        'assets/images/categories/boneless_chicken.png',
      );
    });

    test('should return appropriate fallback icons', () {
      expect(
        CategoryImageService.getCategoryFallbackIcon('chicken'),
        Icons.restaurant_menu,
      );
      
      expect(
        CategoryImageService.getCategoryFallbackIcon('plant based'),
        Icons.eco,
      );
      
      expect(
        CategoryImageService.getCategoryFallbackIcon('ready to cook'),
        Icons.microwave,
      );
      
      // Unknown category should return default icon
      expect(
        CategoryImageService.getCategoryFallbackIcon('unknown'),
        Icons.restaurant,
      );
    });

    test('should return appropriate colors for categories', () {
      expect(
        CategoryImageService.getCategoryColor('chicken'),
        Colors.orange,
      );
      
      expect(
        CategoryImageService.getCategoryColor('fish'),
        Colors.blue,
      );
      
      expect(
        CategoryImageService.getCategoryColor('plant based'),
        Colors.green,
      );
      
      // Unknown category should return default color
      expect(
        CategoryImageService.getCategoryColor('unknown'),
        Colors.grey,
      );
    });

    test('should correctly identify if category has image', () {
      expect(
        CategoryImageService.hasCategoryImage('chicken'),
        true,
      );
      
      expect(
        CategoryImageService.hasCategoryImage('fish'),
        true,
      );
      
      expect(
        CategoryImageService.hasCategoryImage('unknown_category'),
        false,
      );
    });

    test('should return all available categories', () {
      final categories = CategoryImageService.getAllCategories();
      
      expect(categories, isA<List<String>>());
      expect(categories.contains('chicken'), true);
      expect(categories.contains('fish'), true);
      expect(categories.contains('mutton'), true);
      expect(categories.length, greaterThan(5));
    });

    testWidgets('should build product image widget correctly', (WidgetTester tester) async {
      // Test with empty product image URL (should use category fallback)
      final widget = CategoryImageService.buildProductImage(
        productImageUrl: '',
        category: 'chicken',
        width: 100,
        height: 100,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: widget,
          ),
        ),
      );

      // Should not throw any errors
      expect(find.byType(Image), findsOneWidget);
    });
  });
}
