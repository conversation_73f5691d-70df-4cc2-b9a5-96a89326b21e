/// Represents a product within a combo deal
class ComboProduct {
  final String productId;
  final String productName;
  final int quantity;
  final double originalPrice;
  final String? imageUrl;

  ComboProduct({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.originalPrice,
    this.imageUrl,
  });

  factory ComboProduct.fromMap(Map<String, dynamic> data) {
    return ComboProduct(
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      quantity: data['quantity'] ?? 1,
      originalPrice: (data['originalPrice'] ?? 0).toDouble(),
      imageUrl: data['imageUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'originalPrice': originalPrice,
      'imageUrl': imageUrl,
    };
  }
}

/// Represents a combo deal with multiple products
class ComboDeal {
  final String id;
  final String name;
  final String description;
  final List<ComboProduct> products;
  final double originalTotalPrice;
  final double comboPrice;
  final String? imageUrl;
  final bool isActive;
  final DateTime? validFrom;
  final DateTime? validUntil;
  final int maxQuantityPerOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  int quantity; // For cart functionality

  ComboDeal({
    required this.id,
    required this.name,
    required this.description,
    required this.products,
    required this.originalTotalPrice,
    required this.comboPrice,
    this.imageUrl,
    this.isActive = true,
    this.validFrom,
    this.validUntil,
    this.maxQuantityPerOrder = 5,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.quantity = 0,
  });

  // Factory constructor from Firestore data
  factory ComboDeal.fromFirestore(Map<String, dynamic> data, String documentId) {
    return ComboDeal(
      id: documentId,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      products: _parseProducts(data['products']),
      originalTotalPrice: (data['originalTotalPrice'] ?? 0).toDouble(),
      comboPrice: (data['comboPrice'] ?? 0).toDouble(),
      imageUrl: data['imageUrl'],
      isActive: data['isActive'] ?? true,
      validFrom: _parseDateTime(data['validFrom']),
      validUntil: _parseDateTime(data['validUntil']),
      maxQuantityPerOrder: data['maxQuantityPerOrder'] ?? 5,
      createdAt: _parseDateTime(data['createdAt']) ?? DateTime.now(),
      updatedAt: _parseDateTime(data['updatedAt']) ?? DateTime.now(),
      createdBy: data['createdBy'] ?? '',
    );
  }

  // Business logic methods
  bool get isCurrentlyValid {
    final now = DateTime.now();
    if (validFrom != null && now.isBefore(validFrom!)) return false;
    if (validUntil != null && now.isAfter(validUntil!)) return false;
    return isActive;
  }

  double get discountAmount => originalTotalPrice - comboPrice;
  double get discountPercentage => originalTotalPrice > 0 ? ((originalTotalPrice - comboPrice) / originalTotalPrice) * 100 : 0;
  bool get hasDiscount => comboPrice < originalTotalPrice;

  String get formattedComboPrice => '₹${comboPrice.toStringAsFixed(2)}';
  String get formattedOriginalPrice => '₹${originalTotalPrice.toStringAsFixed(2)}';
  String get formattedDiscountPercentage => '${discountPercentage.toStringAsFixed(0)}% OFF';
  String get formattedDiscountAmount => 'Save ₹${discountAmount.toStringAsFixed(2)}';

  int get totalProductCount => products.fold(0, (sum, product) => sum + product.quantity);

  // Get display image - combo image or first product image
  String get displayImageUrl {
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return imageUrl!;
    }
    // Try to get first product image
    for (final product in products) {
      if (product.imageUrl != null && product.imageUrl!.isNotEmpty) {
        return product.imageUrl!;
      }
    }
    return '';
  }

  // Get products summary for display
  String get productsSummary {
    if (products.isEmpty) return '';
    if (products.length == 1) {
      final product = products.first;
      return '${product.quantity}x ${product.productName}';
    }
    if (products.length <= 3) {
      return products.map((p) => '${p.quantity}x ${p.productName}').join(', ');
    }
    return '${products.take(2).map((p) => '${p.quantity}x ${p.productName}').join(', ')} + ${products.length - 2} more';
  }

  // Helper methods
  static List<ComboProduct> _parseProducts(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((item) {
        if (item is Map<String, dynamic>) {
          return ComboProduct.fromMap(item);
        }
        return ComboProduct(
          productId: '',
          productName: '',
          quantity: 1,
          originalPrice: 0.0,
        );
      }).toList();
    }
    return [];
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return null;
      }
    }
    // Handle Firestore Timestamp
    if (value.runtimeType.toString() == 'Timestamp') {
      try {
        return (value as dynamic).toDate();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Create a copy with updated quantity
  ComboDeal copyWith({
    String? id,
    String? name,
    String? description,
    List<ComboProduct>? products,
    double? originalTotalPrice,
    double? comboPrice,
    String? imageUrl,
    bool? isActive,
    DateTime? validFrom,
    DateTime? validUntil,
    int? maxQuantityPerOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    int? quantity,
  }) {
    return ComboDeal(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      products: products ?? this.products,
      originalTotalPrice: originalTotalPrice ?? this.originalTotalPrice,
      comboPrice: comboPrice ?? this.comboPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      validFrom: validFrom ?? this.validFrom,
      validUntil: validUntil ?? this.validUntil,
      maxQuantityPerOrder: maxQuantityPerOrder ?? this.maxQuantityPerOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      quantity: quantity ?? this.quantity,
    );
  }

  // Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'products': products.map((p) => p.toMap()).toList(),
      'originalTotalPrice': originalTotalPrice,
      'comboPrice': comboPrice,
      'imageUrl': imageUrl,
      'isActive': isActive,
      'validFrom': validFrom?.toIso8601String(),
      'validUntil': validUntil?.toIso8601String(),
      'maxQuantityPerOrder': maxQuantityPerOrder,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'quantity': quantity,
    };
  }

  // Create from map
  factory ComboDeal.fromMap(Map<String, dynamic> map) {
    return ComboDeal(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      products: _parseProducts(map['products']),
      originalTotalPrice: (map['originalTotalPrice'] ?? 0).toDouble(),
      comboPrice: (map['comboPrice'] ?? 0).toDouble(),
      imageUrl: map['imageUrl'],
      isActive: map['isActive'] ?? true,
      validFrom: _parseDateTime(map['validFrom']),
      validUntil: _parseDateTime(map['validUntil']),
      maxQuantityPerOrder: map['maxQuantityPerOrder'] ?? 5,
      createdAt: _parseDateTime(map['createdAt']) ?? DateTime.now(),
      updatedAt: _parseDateTime(map['updatedAt']) ?? DateTime.now(),
      createdBy: map['createdBy'] ?? '',
      quantity: map['quantity'] ?? 0,
    );
  }
}
