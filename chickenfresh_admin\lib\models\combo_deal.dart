import 'package:cloud_firestore/cloud_firestore.dart';

/// Represents a product within a combo deal
class ComboProduct {
  final String productId;
  final String productName;
  final int quantity;
  final double originalPrice;
  final String? imageUrl;

  ComboProduct({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.originalPrice,
    this.imageUrl,
  });

  factory ComboProduct.fromMap(Map<String, dynamic> data) {
    return ComboProduct(
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      quantity: data['quantity'] ?? 1,
      originalPrice: (data['originalPrice'] ?? 0).toDouble(),
      imageUrl: data['imageUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'originalPrice': originalPrice,
      'imageUrl': imageUrl,
    };
  }

  ComboProduct copyWith({
    String? productId,
    String? productName,
    int? quantity,
    double? originalPrice,
    String? imageUrl,
  }) {
    return ComboProduct(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      originalPrice: originalPrice ?? this.originalPrice,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}

/// Represents a combo deal with multiple products
class ComboDeal {
  final String id;
  final String name;
  final String description;
  final List<ComboProduct> products;
  final double originalTotalPrice;
  final double comboPrice;
  final String? imageUrl;
  final bool isActive;
  final DateTime? validFrom;
  final DateTime? validUntil;
  final int maxQuantityPerOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;

  ComboDeal({
    required this.id,
    required this.name,
    required this.description,
    required this.products,
    required this.originalTotalPrice,
    required this.comboPrice,
    this.imageUrl,
    this.isActive = true,
    this.validFrom,
    this.validUntil,
    this.maxQuantityPerOrder = 5,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  // Factory constructor from Firestore document
  factory ComboDeal.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ComboDeal(
      id: doc.id,
      name: _validateString(data['name'], 'name'),
      description: _validateString(data['description'], 'description'),
      products: _parseProducts(data['products']),
      originalTotalPrice: _validateDouble(data['originalTotalPrice'], 'originalTotalPrice'),
      comboPrice: _validateDouble(data['comboPrice'], 'comboPrice'),
      imageUrl: data['imageUrl'] as String?,
      isActive: data['isActive'] as bool? ?? true,
      validFrom: _parseDateTime(data['validFrom']),
      validUntil: _parseDateTime(data['validUntil']),
      maxQuantityPerOrder: data['maxQuantityPerOrder'] as int? ?? 5,
      createdAt: _validateTimestamp(data['createdAt'], 'createdAt'),
      updatedAt: _validateTimestamp(data['updatedAt'], 'updatedAt'),
      createdBy: _validateString(data['createdBy'], 'createdBy', defaultValue: 'system'),
    );
  }

  // Factory constructor from JSON
  factory ComboDeal.fromJson(Map<String, dynamic> json) {
    return ComboDeal(
      id: json['id'] ?? '',
      name: _validateString(json['name'], 'name'),
      description: _validateString(json['description'], 'description'),
      products: _parseProducts(json['products']),
      originalTotalPrice: _validateDouble(json['originalTotalPrice'], 'originalTotalPrice'),
      comboPrice: _validateDouble(json['comboPrice'], 'comboPrice'),
      imageUrl: json['imageUrl'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      validFrom: _parseDateTime(json['validFrom']),
      validUntil: _parseDateTime(json['validUntil']),
      maxQuantityPerOrder: json['maxQuantityPerOrder'] as int? ?? 5,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      createdBy: _validateString(json['createdBy'], 'createdBy', defaultValue: 'system'),
    );
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'products': products.map((p) => p.toMap()).toList(),
      'originalTotalPrice': originalTotalPrice,
      'comboPrice': comboPrice,
      'imageUrl': imageUrl,
      'isActive': isActive,
      'validFrom': validFrom != null ? Timestamp.fromDate(validFrom!) : null,
      'validUntil': validUntil != null ? Timestamp.fromDate(validUntil!) : null,
      'maxQuantityPerOrder': maxQuantityPerOrder,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
    };
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'products': products.map((p) => p.toMap()).toList(),
      'originalTotalPrice': originalTotalPrice,
      'comboPrice': comboPrice,
      'imageUrl': imageUrl,
      'isActive': isActive,
      'validFrom': validFrom?.toIso8601String(),
      'validUntil': validUntil?.toIso8601String(),
      'maxQuantityPerOrder': maxQuantityPerOrder,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
    };
  }

  // Create a copy with updated fields
  ComboDeal copyWith({
    String? id,
    String? name,
    String? description,
    List<ComboProduct>? products,
    double? originalTotalPrice,
    double? comboPrice,
    String? imageUrl,
    bool? isActive,
    DateTime? validFrom,
    DateTime? validUntil,
    int? maxQuantityPerOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return ComboDeal(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      products: products ?? this.products,
      originalTotalPrice: originalTotalPrice ?? this.originalTotalPrice,
      comboPrice: comboPrice ?? this.comboPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      validFrom: validFrom ?? this.validFrom,
      validUntil: validUntil ?? this.validUntil,
      maxQuantityPerOrder: maxQuantityPerOrder ?? this.maxQuantityPerOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  // Business logic methods
  bool get isCurrentlyValid {
    final now = DateTime.now();
    if (validFrom != null && now.isBefore(validFrom!)) return false;
    if (validUntil != null && now.isAfter(validUntil!)) return false;
    return isActive;
  }

  double get discountAmount => originalTotalPrice - comboPrice;
  double get discountPercentage => originalTotalPrice > 0 ? ((originalTotalPrice - comboPrice) / originalTotalPrice) * 100 : 0;
  bool get hasDiscount => comboPrice < originalTotalPrice;

  String get formattedComboPrice => '₹${comboPrice.toStringAsFixed(2)}';
  String get formattedOriginalPrice => '₹${originalTotalPrice.toStringAsFixed(2)}';
  String get formattedDiscountPercentage => '${discountPercentage.toStringAsFixed(0)}% OFF';
  String get formattedDiscountAmount => 'Save ₹${discountAmount.toStringAsFixed(2)}';

  int get totalProductCount => products.fold(0, (sum, product) => sum + product.quantity);

  // Helper methods for validation
  static String _validateString(dynamic value, String fieldName, {String defaultValue = ''}) {
    if (value == null || (value is String && value.trim().isEmpty)) {
      if (defaultValue.isEmpty) {
        throw ArgumentError('$fieldName cannot be null or empty');
      }
      return defaultValue;
    }
    return value.toString().trim();
  }

  static double _validateDouble(dynamic value, String fieldName, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is num) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed == null) {
        throw ArgumentError('Invalid $fieldName: $value');
      }
      return parsed;
    }
    throw ArgumentError('Invalid $fieldName type: ${value.runtimeType}');
  }

  static DateTime _validateTimestamp(dynamic value, String fieldName) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    throw ArgumentError('Invalid $fieldName type: ${value.runtimeType}');
  }

  static List<ComboProduct> _parseProducts(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((item) {
        if (item is Map<String, dynamic>) {
          return ComboProduct.fromMap(item);
        }
        throw ArgumentError('Invalid product format in combo deal');
      }).toList();
    }
    throw ArgumentError('Products must be a list');
  }

  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    return null;
  }
}
