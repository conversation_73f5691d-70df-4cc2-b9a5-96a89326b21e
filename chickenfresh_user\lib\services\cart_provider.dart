
import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../models/combo_deal_model.dart';

class CartProvider with ChangeNotifier {
  final List<Product> _products = [];
  final List<ComboDeal> _comboDeals = [];

  List<Product> get products => _products;
  List<ComboDeal> get comboDeals => _comboDeals;

  double get totalPrice =>
    _products.fold(0.0, (sum, item) => sum + (item.price * item.quantity)) +
    _comboDeals.fold(0.0, (sum, item) => sum + (item.comboPrice * item.quantity));

  double get totalSavings =>
    _comboDeals.fold(0.0, (sum, item) => sum + (item.discountAmount * item.quantity));

  int get totalItems =>
    _products.where((p) => p.quantity > 0).fold(0, (sum, item) => sum + item.quantity) +
    _comboDeals.where((c) => c.quantity > 0).fold(0, (sum, item) => sum + item.quantity);

  void addProduct(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity++;
    } else {
      // Start with quantity 1 when first added to cart
      product.quantity = 1;
      _products.add(product);
    }
    notifyListeners();
  }

  void removeProduct(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      if (_products[index].quantity > 1) {
        _products[index].quantity--;
      } else {
        // Set quantity to 0 instead of removing from list
        _products[index].quantity = 0;
      }
    }
    notifyListeners();
  }

  void clearCart() {
    _products.clear();
    notifyListeners();
  }

  // Add product to list without changing quantity (for initial loading)
  void addProductToList(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index == -1) {
      _products.add(product);
      notifyListeners();
    }
  }

  // Get number of unique products in cart
  int get uniqueItemsCount => _products.where((p) => p.quantity > 0).length;

  // Get number of unique combo deals in cart
  int get uniqueComboDealsCount => _comboDeals.where((c) => c.quantity > 0).length;

  // Check if cart is empty
  bool get isEmpty => _products.every((p) => p.quantity == 0) && _comboDeals.every((c) => c.quantity == 0);

  // Check if cart has items
  bool get isNotEmpty => _products.any((p) => p.quantity > 0) || _comboDeals.any((c) => c.quantity > 0);

  // Get cart items (products with quantity > 0)
  List<Product> get cartItems => _products.where((p) => p.quantity > 0).toList();

  // Remove specific product completely from cart
  void removeProductCompletely(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity = 0;
    }
    notifyListeners();
  }

  // Update product quantity directly
  void updateProductQuantity(Product product, int quantity) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity = quantity.clamp(0, 999); // Max 999 items
    }
    notifyListeners();
  }

  // Get quantity of specific product
  int getProductQuantity(String productId) {
    int index = _products.indexWhere((p) => p.id == productId);
    return index != -1 ? _products[index].quantity : 0;
  }

  // Check if product is in cart
  bool isProductInCart(String productId) {
    return _products.any((p) => p.id == productId && p.quantity > 0);
  }

  // Combo Deal Methods
  void addComboDeal(ComboDeal comboDeal) {
    int index = _comboDeals.indexWhere((c) => c.id == comboDeal.id);
    if (index != -1) {
      if (_comboDeals[index].quantity < comboDeal.maxQuantityPerOrder) {
        _comboDeals[index].quantity++;
      }
    } else {
      // Start with quantity 1 when first added to cart
      comboDeal.quantity = 1;
      _comboDeals.add(comboDeal);
    }
    notifyListeners();
  }

  void removeComboDeal(ComboDeal comboDeal) {
    int index = _comboDeals.indexWhere((c) => c.id == comboDeal.id);
    if (index != -1) {
      if (_comboDeals[index].quantity > 1) {
        _comboDeals[index].quantity--;
      } else {
        // Set quantity to 0 instead of removing from list
        _comboDeals[index].quantity = 0;
      }
    }
    notifyListeners();
  }

  void removeComboCompletely(ComboDeal comboDeal) {
    int index = _comboDeals.indexWhere((c) => c.id == comboDeal.id);
    if (index != -1) {
      _comboDeals[index].quantity = 0;
    }
    notifyListeners();
  }

  // Get cart combo deals (combo deals with quantity > 0)
  List<ComboDeal> get cartComboDeals => _comboDeals.where((c) => c.quantity > 0).toList();

  // Check if combo deal is in cart
  bool isComboDealInCart(String comboDealId) {
    return _comboDeals.any((c) => c.id == comboDealId && c.quantity > 0);
  }

  // Get combo deal quantity in cart
  int getComboDealQuantity(String comboDealId) {
    final comboDeal = _comboDeals.firstWhere(
      (c) => c.id == comboDealId,
      orElse: () => ComboDeal(
        id: '',
        name: '',
        description: '',
        products: [],
        originalTotalPrice: 0,
        comboPrice: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: '',
      ),
    );
    return comboDeal.quantity;
  }

  // Clear all items from cart
  void clearAllCart() {
    for (var product in _products) {
      product.quantity = 0;
    }
    for (var comboDeal in _comboDeals) {
      comboDeal.quantity = 0;
    }
    notifyListeners();
  }
}
