import 'package:flutter/material.dart';

/// A modern, reusable button widget with gradient and shadow effects
class ModernButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color primaryColor;
  final Color? secondaryColor;
  final bool isLoading;
  final IconData? icon;
  final double height;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final bool isOutlined;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.primaryColor = Colors.blue,
    this.secondaryColor,
    this.isLoading = false,
    this.icon,
    this.height = 56,
    this.width,
    this.padding,
    this.textStyle,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveSecondaryColor = secondaryColor ?? _getDarkerShade(primaryColor);
    
    if (isOutlined) {
      return _buildOutlinedButton(context);
    }
    
    return Container(
      width: width ?? double.infinity,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            primaryColor,
            effectiveSecondaryColor,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: onPressed != null ? [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: padding ?? EdgeInsets.zero,
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              )
            : _buildButtonContent(),
      ),
    );
  }

  Widget _buildOutlinedButton(BuildContext context) {
    return Container(
      width: width ?? double.infinity,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: primaryColor,
          width: 2,
        ),
      ),
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.white,
          side: BorderSide.none,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: padding ?? EdgeInsets.zero,
        ),
        child: isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  strokeWidth: 2,
                ),
              )
            : _buildButtonContent(isOutlined: true),
      ),
    );
  }

  Widget _buildButtonContent({bool isOutlined = false}) {
    final effectiveTextStyle = textStyle ?? TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: isOutlined ? primaryColor : Colors.white,
    );

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isOutlined ? primaryColor : Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(text, style: effectiveTextStyle),
        ],
      );
    }

    return Text(text, style: effectiveTextStyle);
  }

  Color _getDarkerShade(Color color) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness - 0.1).clamp(0.0, 1.0)).toColor();
  }
}

/// A modern loading button that shows loading state
class ModernLoadingButton extends StatelessWidget {
  final String text;
  final String loadingText;
  final Future<void> Function()? onPressed;
  final Color primaryColor;
  final Color? secondaryColor;
  final IconData? icon;
  final double height;
  final double? width;
  final bool isOutlined;

  const ModernLoadingButton({
    super.key,
    required this.text,
    this.loadingText = 'Loading...',
    this.onPressed,
    this.primaryColor = Colors.blue,
    this.secondaryColor,
    this.icon,
    this.height = 56,
    this.width,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    return StatefulBuilder(
      builder: (context, setState) {
        bool isLoading = false;

        return ModernButton(
          text: isLoading ? loadingText : text,
          onPressed: isLoading ? null : () async {
            if (onPressed != null) {
              setState(() => isLoading = true);
              try {
                await onPressed!();
              } finally {
                if (context.mounted) {
                  setState(() => isLoading = false);
                }
              }
            }
          },
          primaryColor: primaryColor,
          secondaryColor: secondaryColor,
          isLoading: isLoading,
          icon: isLoading ? null : icon,
          height: height,
          width: width,
          isOutlined: isOutlined,
        );
      },
    );
  }
}

/// A modern text button with consistent styling
class ModernTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color color;
  final double fontSize;
  final FontWeight fontWeight;
  final EdgeInsetsGeometry? padding;

  const ModernTextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.color = Colors.blue,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w500,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        padding: padding ?? const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
        ),
      ),
    );
  }
}
