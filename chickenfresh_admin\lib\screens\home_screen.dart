import 'dart:async';
import 'package:flutter/material.dart';
import 'package:chickenfresh_admin/screens/products_screen.dart';
import 'package:chickenfresh_admin/screens/orders_screen.dart';
import 'package:chickenfresh_admin/screens/analytics_screen.dart';
import 'package:chickenfresh_admin/screens/payment_status_screen.dart';
import 'package:chickenfresh_admin/screens/settings_screen.dart';
import 'package:chickenfresh_admin/screens/banner_management_screen.dart';
import 'package:chickenfresh_admin/screens/payment_settings_screen.dart';
import '../services/auth_service.dart';
import '../services/analytics_service.dart';
import '../services/simple_notification_service.dart';
import '../models/order.dart' as order_model;

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final AnalyticsService _analyticsService = AnalyticsService();
  final SimpleNotificationService _notificationService = SimpleNotificationService();

  DashboardAnalytics? _analytics;
  bool _isLoading = true;
  bool _isRefreshing = false;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _notificationService.initialize();
    _loadDashboardData();

    // Initialize notification service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // No additional setup needed for simplified service
    });

    // Start refresh timer to update data every second
    _startRefreshTimer();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _notificationService.dispose();
    super.dispose();
  }

  void _startRefreshTimer() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && !_isRefreshing) {
        _refreshDashboardData();
      }
    });
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final analytics = await _analyticsService.getDashboardAnalytics();

      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading dashboard: $e')),
        );
      }
    }
  }

  Future<void> _refreshDashboardData() async {
    if (_isRefreshing) return; // Prevent overlapping refresh requests

    setState(() {
      _isRefreshing = true;
    });

    try {
      final analytics = await _analyticsService.getDashboardAnalytics();

      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isRefreshing = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        // Silent refresh - don't show error snackbar for background refreshes
      }
    }
  }

  Future<void> _logout(BuildContext context) async {
    try {
      await AuthService().signOut();
      // Navigation will be handled automatically by AuthWrapper
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error logging out: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('chickenfresh Admin Dashboard'),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Notification bell
          StreamBuilder<List<SimpleNotification>>(
            stream: _notificationService.notificationStream,
            builder: (context, snapshot) {
              final notificationCount = snapshot.data?.length ?? 0;
              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications),
                    onPressed: () => _showNotifications(context),
                    tooltip: 'Notifications',
                  ),
                  if (notificationCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '$notificationCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
            tooltip: 'Logout',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading dashboard...'),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _refreshDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.green.shade50,
                        Colors.white,
                      ],
                    ),
                  ),
        child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Analytics Overview Cards
                        if (_analytics != null) ...[
                          _buildAnalyticsOverview(),
                          const SizedBox(height: 20),
                        ],



                        // Status Updates Dashboard
                        if (_analytics != null) ...[
                          _buildStatusUpdatesDashboard(),
                          const SizedBox(height: 20),
                        ],

                        // Action Buttons Grid
                        _buildActionButtonsGrid(),
                      ],
                    ),
                ),
              ),
            ),
          ),
    );
  }

  Widget _buildAnalyticsOverview() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue.shade600, size: 28),
                const SizedBox(width: 12),
                Text(
                  'Analytics Overview',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _navigateToAnalytics(),
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Today Completed',
                    '${_analytics!.todayOrders}',
                    Icons.shopping_cart,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Today Revenue',
                    '₹${_analytics!.todayRevenue.toStringAsFixed(0)}',
                    Icons.currency_rupee,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Pending Orders',
                    '${_analytics!.pendingOrders}',
                    Icons.pending,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Delivered Today',
                    '${_analytics!.todayDeliveredOrders}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildStatusUpdatesDashboard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.update, color: Colors.indigo.shade600, size: 28),
                const SizedBox(width: 12),
                Text(
                  'Order Status Updates',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    'Pending',
                    '${_analytics!.pendingOrders}',
                    Colors.orange,
                    order_model.OrderStatus.pending,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Confirmed',
                    '${_analytics!.confirmedOrders}',
                    Colors.blue,
                    order_model.OrderStatus.confirmed,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Processing',
                    '${_analytics!.processingOrders}',
                    Colors.purple,
                    order_model.OrderStatus.processing,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    'Out for Delivery',
                    '${_analytics!.outForDeliveryOrders}',
                    Colors.amber,
                    order_model.OrderStatus.outForDelivery,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Delivered',
                    '${_analytics!.deliveredOrders}',
                    Colors.green,
                    order_model.OrderStatus.delivered,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    'Cancelled',
                    '${_analytics!.cancelledOrders + _analytics!.partialCancelledOrders}',
                    Colors.red,
                    order_model.OrderStatus.cancelled,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(String title, String count, Color color, order_model.OrderStatus status) {
    return InkWell(
      onTap: () => _navigateToOrdersByStatus(status),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Text(
              count,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 10,
                color: color.withValues(alpha: 0.6),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtonsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.0,
          children: [
            _buildActionCard(
              title: 'Analytics',
              subtitle: 'View detailed reports',
              icon: Icons.analytics,
              color: Colors.blue,
              onTap: _navigateToAnalytics,
            ),
            _buildActionCard(
              title: 'Products',
              subtitle: 'Manage inventory',
              icon: Icons.inventory_2,
              color: Colors.green,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProductsScreen()),
              ),
            ),
            _buildActionCard(
              title: 'Orders',
              subtitle: 'Process orders',
              icon: Icons.shopping_cart,
              color: Colors.orange,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const OrdersScreen()),
              ),
            ),


            _buildActionCard(
              title: 'Payment Status',
              subtitle: 'Cash handover tracking',
              icon: Icons.payment,
              color: Colors.indigo,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const PaymentStatusScreen()),
              ),
            ),
            _buildActionCard(
              title: 'Banners',
              subtitle: 'Manage app banners',
              icon: Icons.image,
              color: Colors.pink,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const BannerManagementScreen()),
              ),
            ),
            _buildActionCard(
              title: 'Payment Settings',
              subtitle: 'Configure payment gateways',
              icon: Icons.payment,
              color: Colors.indigo,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const PaymentSettingsScreen()),
              ),
            ),
            _buildActionCard(
              title: 'Settings',
              subtitle: 'App configuration',
              icon: Icons.settings,
              color: Colors.grey,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Navigation and action methods
  void _navigateToAnalytics() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AnalyticsScreen()),
    );
  }

  void _navigateToOrdersByStatus(order_model.OrderStatus status) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => OrdersScreen(initialStatus: status),
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) {
          return StreamBuilder<List<SimpleNotification>>(
            stream: _notificationService.notificationStream,
            builder: (context, snapshot) {
              final notifications = snapshot.data ?? [];

              return Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with close button
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Notifications',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                          IconButton(
                            icon: Icon(Icons.close, color: Colors.green.shade700),
                            onPressed: () => Navigator.pop(context),
                            tooltip: 'Close',
                          ),
                        ],
                      ),
                    ),
                    // Content area
                    Expanded(
                      child: notifications.isEmpty
                          ? const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.notifications_none, size: 64, color: Colors.grey),
                                  SizedBox(height: 16),
                                  Text('No notifications'),
                                ],
                              ),
                            )
                          : Column(
                              children: [
                                // Clear All button
                                if (notifications.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          '${notifications.length} notification${notifications.length == 1 ? '' : 's'}',
                                          style: TextStyle(
                                            color: Colors.grey.shade600,
                                            fontSize: 14,
                                          ),
                                        ),
                                        TextButton.icon(
                                          onPressed: () => _clearAllNotifications(),
                                          icon: const Icon(Icons.clear_all, size: 18),
                                          label: const Text('Clear All'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: Colors.red.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                // Notifications list
                                Expanded(
                                  child: ListView.builder(
                                    controller: scrollController,
                                    itemCount: notifications.length,
                                    itemBuilder: (context, index) {
                                      final notification = notifications[index];
                                      return Card(
                                        margin: const EdgeInsets.only(bottom: 8, left: 8, right: 8),
                                        elevation: 2,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(12),
                                          side: BorderSide(
                                            color: _getPriorityColor(notification.priority),
                                            width: 2,
                                          ),
                                        ),
                                        child: ListTile(
                                          leading: Stack(
                                            children: [
                                              CircleAvatar(
                                                backgroundColor: _getNotificationColor(notification.type),
                                                child: Icon(
                                                  _getNotificationIcon(notification.type),
                                                  color: Colors.white,
                                                  size: 20,
                                                ),
                                              ),
                                              if (notification.priority == NotificationPriority.high ||
                                                  notification.priority == NotificationPriority.critical)
                                                Positioned(
                                                  right: 0,
                                                  top: 0,
                                                  child: Container(
                                                    width: 12,
                                                    height: 12,
                                                    decoration: BoxDecoration(
                                                      color: notification.priority == NotificationPriority.critical
                                                          ? Colors.red
                                                          : Colors.orange,
                                                      shape: BoxShape.circle,
                                                      border: Border.all(color: Colors.white, width: 1),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                          title: Text(
                                            notification.title,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 14,
                                            ),
                                          ),
                                          subtitle: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              const SizedBox(height: 4),
                                              Text(
                                                notification.message,
                                                style: const TextStyle(fontSize: 13),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                _formatTime(notification.createdAt),
                                                style: TextStyle(
                                                  fontSize: 11,
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                            ],
                                          ),
                                          trailing: IconButton(
                                            icon: const Icon(Icons.close, size: 18),
                                            onPressed: () => _clearNotification(notification.id),
                                            tooltip: 'Clear notification',
                                            color: Colors.grey.shade600,
                                          ),
                                          isThreeLine: true,
                                          onTap: () => _handleNotificationTap(notification),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }





  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.newOrder:
        return Colors.green;
      case NotificationType.orderFollowUp:
        return Colors.orange;
      case NotificationType.orderDelivered:
        return Colors.blue;
      case NotificationType.stockAlert:
        return Colors.red;
      case NotificationType.systemAlert:
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.newOrder:
        return Icons.shopping_cart;
      case NotificationType.orderFollowUp:
        return Icons.schedule;
      case NotificationType.orderDelivered:
        return Icons.check_circle;
      case NotificationType.stockAlert:
        return Icons.warning;
      case NotificationType.systemAlert:
        return Icons.info;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey.shade300;
      case NotificationPriority.medium:
        return Colors.blue.shade300;
      case NotificationPriority.high:
        return Colors.orange.shade300;
      case NotificationPriority.critical:
        return Colors.red.shade300;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  // Clear individual notification
  void _clearNotification(String notificationId) {
    _notificationService.removeNotification(notificationId);
  }

  // Clear all notifications
  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _notificationService.clearAllNotifications();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  // Handle notification tap
  void _handleNotificationTap(SimpleNotification notification) {
    // Mark as read when tapped
    _clearNotification(notification.id);

    // Navigate based on notification type
    switch (notification.type) {
      case NotificationType.newOrder:
      case NotificationType.orderFollowUp:
      case NotificationType.orderDelivered:
        // Navigate to orders screen
        Navigator.pushNamed(context, '/orders');
        break;
      case NotificationType.stockAlert:
        // Navigate to products screen
        Navigator.pushNamed(context, '/products');
        break;
      case NotificationType.systemAlert:
        // Show popup with notification details
        SimpleNotificationService.showPopupNotification(
          context,
          title: notification.title,
          message: notification.message,
          icon: Icons.info,
          backgroundColor: Colors.blue.shade600,
        );
        break;
    }
  }
}
