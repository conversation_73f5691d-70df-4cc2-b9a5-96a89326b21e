// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'config/environment.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: Environment.firebaseApiKey,
    appId: Environment.firebaseAppId,
    messagingSenderId: Environment.firebaseMessagingSenderId,
    projectId: Environment.firebaseProjectId,
    authDomain: Environment.firebaseAuthDomain,
    storageBucket: Environment.firebaseStorageBucket,
    measurementId: Environment.firebaseMeasurementId,
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:android:6a3808febc5f65075ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:ios:6a3808febc5f65075ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
    iosBundleId: 'com.chickenfresh.admin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:macos:6a3808febc5f65075ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
    iosBundleId: 'com.chickenfresh.admin',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:windows:6a3808febc5f65075ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
  );
}
