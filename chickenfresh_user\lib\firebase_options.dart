// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:web:e57dddb1d1e1e0745ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    authDomain: 'chickenfresh-cae87.firebaseapp.com',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
    measurementId: 'G-1234567890',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:android:e57dddb1d1e1e0745ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:ios:e57dddb1d1e1e0745ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
    iosBundleId: 'com.chickenfresh.user',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:macos:e57dddb1d1e1e0745ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
    iosBundleId: 'com.chickenfresh.user',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCm3yoqOnCljn61-dI22-F3pPghxzIuzRI',
    appId: '1:473594197891:windows:e57dddb1d1e1e0745ae8b6',
    messagingSenderId: '473594197891',
    projectId: 'chickenfresh-cae87',
    storageBucket: 'chickenfresh-cae87.firebasestorage.app',
  );
}
